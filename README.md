# 🏦 VaultBoard - Bank Admin System

A comprehensive bank administration system built with Symfony 6.4, designed for managing clients, accounts, cards, and transactions with full audit logging and role-based access control.

## 🌟 Features

### ✅ **Implemented Features**
- **👥 Client Management**: Full CRUD operations with search and filtering
- **🏛️ Account Management**: One active account per client rule enforcement
- **💳 Card Management**: One active card per account with expiry tracking
- **💰 Transaction Processing**: Complete transaction history and status tracking
- **🔐 Admin Authentication**: Role-based access (SuperAdmin, Manager, Auditor)
- **📊 Dashboard & Reports**: Real-time analytics and system overview
- **📝 Audit Logging**: Complete action tracking for compliance
- **🔔 Notification System**: System alerts and notifications
- **🎨 Modern UI**: Responsive Bootstrap 5 interface with VaultBoard branding

### 🏗️ **Architecture**
- **Framework**: Symfony 6.4 with PHP 8.2+
- **Database**: MySQL with Doctrine ORM
- **Security**: bcrypt password hashing, CSRF protection, role-based permissions
- **Frontend**: Bootstrap 5, Font Awesome icons, responsive design
- **Patterns**: Repository pattern, Service layer, Event-driven audit logging

## 🚀 Quick Start

### Prerequisites
- PHP 8.2 or higher
- MySQL 8.0 or higher
- Composer

### Installation
1. **Clone and setup**:
   ```bash
   cd VaultBoard
   composer install
   ```

2. **Database setup**:
   - Create MySQL database: `vb_db`
   - Update `.env` if needed (currently configured for localhost/root/no password)
   - Run migrations: `php bin/console doctrine:migrations:migrate`

3. **Create admin user**:
   ```bash
   php bin/console app:create-admin admin admin123 --role=superadmin
   ```

4. **Start development server**:
   ```bash
   php -S localhost:8000 -t public
   ```

5. **Access the system**:
   - URL: http://localhost:8000
   - Username: `admin`
   - Password: `admin123`

## 🔑 Default Login Credentials

| Role | Username | Password | Permissions |
|------|----------|----------|-------------|
| Super Admin | `admin` | `admin123` | Full system access |

## 📊 Database Schema

### Core Tables
- **clients**: Client information with status management
- **accounts**: Account details with one-per-client rule
- **cards**: Card management with one-active-per-account rule
- **transactions**: Complete transaction history
- **admins**: System administrators with role-based access
- **audit_logs**: Complete action tracking
- **notifications**: System alerts and messages

### Business Rules Enforced
- ✅ One active account per client
- ✅ One active card per account
- ✅ Complete audit trail for all actions
- ✅ Role-based permission system

## 🎯 Key Features Demonstrated

### 1. **Client Management**
- Create, view, edit, and manage client profiles
- Search and filter capabilities
- Status management (Active/Inactive/Blocked)
- Business rule enforcement

### 2. **Security & Compliance**
- Secure admin authentication
- Role-based access control
- Complete audit logging
- CSRF protection on all forms

### 3. **Modern UI/UX**
- Responsive Bootstrap 5 design
- Professional VaultBoard branding
- Intuitive navigation and workflows
- Real-time feedback and notifications

### 4. **Scalable Architecture**
- Service layer pattern for business logic
- Repository pattern for data access
- Event-driven audit logging
- Modular design for easy extension

## 🔧 Available Commands

```bash
# Create admin user
php bin/console app:create-admin <username> <password> --role=<role>

# Create test data
php bin/console app:create-test-data

# Database operations
php bin/console doctrine:migrations:migrate
php bin/console doctrine:query:sql "SELECT * FROM clients"
```

## 📈 System Overview

The system currently includes:
- **5 test clients** with complete profiles
- **5 accounts** with different types and balances
- **5 active cards** with proper expiry dates
- **5 sample transactions** showing system activity
- **Complete audit trail** of all admin actions

## 🚀 Future Extensions

The foundation is ready for:
- Advanced account management features
- Card replacement and reissuance workflows
- Complex transaction processing
- Advanced reporting and analytics
- Multi-admin management
- API endpoints for external integrations

## 🏗️ Technical Stack

- **Backend**: Symfony 6.4, Doctrine ORM, PHP 8.2+
- **Database**: MySQL 8.0
- **Frontend**: Bootstrap 5, Font Awesome, Twig templates
- **Security**: Symfony Security Bundle, bcrypt hashing
- **Development**: Symfony CLI, Doctrine Migrations

## 📝 License

This project is developed as a demonstration of modern banking system architecture and best practices.

---

**VaultBoard** - Secure, Scalable, Professional Bank Administration
