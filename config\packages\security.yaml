security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        App\Entity\Admin:
            algorithm: bcrypt
            cost: 12
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        admin_provider:
            entity:
                class: App\Entity\Admin
                property: username
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            lazy: true
            provider: admin_provider
            form_login:
                login_path: admin_login
                check_path: admin_login
                default_target_path: admin_dashboard
                username_parameter: username
                password_parameter: password
            logout:
                path: admin_logout
                target: admin_login
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800 # 1 week
                path: /

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/admin/login, roles: PUBLIC_ACCESS }
        - { path: ^/admin, roles: ROLE_ADMIN }
        - { path: ^/, roles: PUBLIC_ACCESS }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
