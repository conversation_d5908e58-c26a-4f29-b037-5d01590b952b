<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250524170654 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE accounts (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, account_id INT NOT NULL, account_type VARCHAR(20) NOT NULL, balance NUMERIC(12, 2) NOT NULL, status VARCHAR(20) NOT NULL, opened_at DATETIME NOT NULL, closed_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_CAC89EAC9B6B5FBA (account_id), INDEX IDX_CAC89EAC19EB6921 (client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE admins (id INT AUTO_INCREMENT NOT NULL, admin_id INT NOT NULL, username VARCHAR(50) NOT NULL, password_hash VARCHAR(255) NOT NULL, role VARCHAR(20) NOT NULL, created_at DATETIME NOT NULL, status VARCHAR(20) NOT NULL, UNIQUE INDEX UNIQ_A2E0150F642B8210 (admin_id), UNIQUE INDEX UNIQ_A2E0150FF85E0677 (username), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE audit_logs (id INT AUTO_INCREMENT NOT NULL, admin_id INT NOT NULL, log_id INT NOT NULL, action_type VARCHAR(100) NOT NULL, entity VARCHAR(100) DEFAULT NULL, entity_id INT DEFAULT NULL, timestamp DATETIME NOT NULL, details LONGTEXT DEFAULT NULL, ip_address VARCHAR(45) DEFAULT NULL, user_agent LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_D62F2858EA675D86 (log_id), INDEX IDX_D62F2858642B8210 (admin_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE cards (id INT AUTO_INCREMENT NOT NULL, account_id INT NOT NULL, card_id INT NOT NULL, card_number VARCHAR(16) NOT NULL, issued_date DATETIME NOT NULL, expiry_date DATE NOT NULL, status VARCHAR(20) NOT NULL, UNIQUE INDEX UNIQ_4C258FD4ACC9A20 (card_id), UNIQUE INDEX UNIQ_4C258FDE4AF4C20 (card_number), INDEX IDX_4C258FD9B6B5FBA (account_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE clients (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, first_name VARCHAR(100) NOT NULL, last_name VARCHAR(100) NOT NULL, email VARCHAR(150) NOT NULL, phone VARCHAR(20) DEFAULT NULL, created_at DATETIME NOT NULL, status VARCHAR(20) NOT NULL, UNIQUE INDEX UNIQ_C82E7419EB6921 (client_id), UNIQUE INDEX UNIQ_C82E74E7927C74 (email), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE notifications (id INT AUTO_INCREMENT NOT NULL, admin_id INT DEFAULT NULL, notification_id INT NOT NULL, message LONGTEXT NOT NULL, is_read TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, type VARCHAR(20) DEFAULT NULL, related_entity VARCHAR(100) DEFAULT NULL, related_entity_id INT DEFAULT NULL, UNIQUE INDEX UNIQ_6000B0D3EF1A9D84 (notification_id), INDEX IDX_6000B0D3642B8210 (admin_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE transactions (id INT AUTO_INCREMENT NOT NULL, account_id INT NOT NULL, target_account_id INT DEFAULT NULL, transaction_id INT NOT NULL, type VARCHAR(20) NOT NULL, amount NUMERIC(12, 2) NOT NULL, timestamp DATETIME NOT NULL, status VARCHAR(20) NOT NULL, description LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_EAA81A4C2FC0CB0F (transaction_id), INDEX IDX_EAA81A4C9B6B5FBA (account_id), INDEX IDX_EAA81A4CA987872B (target_account_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE messenger_messages (id BIGINT AUTO_INCREMENT NOT NULL, body LONGTEXT NOT NULL, headers LONGTEXT NOT NULL, queue_name VARCHAR(190) NOT NULL, created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', available_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', delivered_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', INDEX IDX_75EA56E0FB7336F0 (queue_name), INDEX IDX_75EA56E0E3BD61CE (available_at), INDEX IDX_75EA56E016BA31DB (delivered_at), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounts ADD CONSTRAINT FK_CAC89EAC19EB6921 FOREIGN KEY (client_id) REFERENCES clients (client_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE audit_logs ADD CONSTRAINT FK_D62F2858642B8210 FOREIGN KEY (admin_id) REFERENCES admins (admin_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cards ADD CONSTRAINT FK_4C258FD9B6B5FBA FOREIGN KEY (account_id) REFERENCES accounts (account_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE notifications ADD CONSTRAINT FK_6000B0D3642B8210 FOREIGN KEY (admin_id) REFERENCES admins (admin_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE transactions ADD CONSTRAINT FK_EAA81A4C9B6B5FBA FOREIGN KEY (account_id) REFERENCES accounts (account_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE transactions ADD CONSTRAINT FK_EAA81A4CA987872B FOREIGN KEY (target_account_id) REFERENCES accounts (account_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE accounts DROP FOREIGN KEY FK_CAC89EAC19EB6921
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE audit_logs DROP FOREIGN KEY FK_D62F2858642B8210
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cards DROP FOREIGN KEY FK_4C258FD9B6B5FBA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE notifications DROP FOREIGN KEY FK_6000B0D3642B8210
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE transactions DROP FOREIGN KEY FK_EAA81A4C9B6B5FBA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE transactions DROP FOREIGN KEY FK_EAA81A4CA987872B
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE accounts
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE admins
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE audit_logs
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE cards
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE clients
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE notifications
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE transactions
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE messenger_messages
        SQL);
    }
}
