<?php

namespace App\Command;

use App\Entity\Admin;
use App\Entity\AdminRole;
use App\Entity\AdminStatus;
use App\Repository\AdminRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsCommand(
    name: 'app:create-admin',
    description: 'Create a new admin user for VaultBoard',
)]
class CreateAdminCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AdminRepository $adminRepository,
        private UserPasswordHasherInterface $passwordHasher
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('username', InputArgument::REQUIRED, 'Admin username')
            ->addArgument('password', InputArgument::REQUIRED, 'Admin password')
            ->addOption('role', 'r', InputOption::VALUE_OPTIONAL, 'Admin role (superadmin, manager, auditor)', 'manager')
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'Force creation even if username exists')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $username = $input->getArgument('username');
        $password = $input->getArgument('password');
        $roleValue = $input->getOption('role');
        $force = $input->getOption('force');

        // Validate role
        $role = AdminRole::tryFrom($roleValue);
        if (!$role) {
            $io->error("Invalid role '{$roleValue}'. Valid roles are: " . implode(', ', array_map(fn($r) => $r->value, AdminRole::cases())));
            return Command::FAILURE;
        }

        // Check if username already exists
        $existingAdmin = $this->adminRepository->findByUsername($username);
        if ($existingAdmin && !$force) {
            $io->error("Admin with username '{$username}' already exists. Use --force to overwrite.");
            return Command::FAILURE;
        }

        if ($existingAdmin && $force) {
            $io->warning("Updating existing admin '{$username}'");
            $admin = $existingAdmin;
        } else {
            $admin = new Admin();
            $admin->setUsername($username);
        }

        // Set admin properties
        $admin->setRole($role);
        $admin->setStatus(AdminStatus::ACTIVE);
        
        // Hash password
        $hashedPassword = $this->passwordHasher->hashPassword($admin, $password);
        $admin->setPassword($hashedPassword);

        // Save admin
        $this->entityManager->persist($admin);
        $this->entityManager->flush();

        $io->success([
            'Admin user created successfully!',
            "Username: {$username}",
            "Role: {$role->getLabel()}",
            "Status: {$admin->getStatus()->getLabel()}",
            "Admin ID: {$admin->getAdminId()}"
        ]);

        if ($role === AdminRole::SUPERADMIN) {
            $io->note('This admin has SUPER ADMIN privileges with full system access.');
        }

        return Command::SUCCESS;
    }
}
