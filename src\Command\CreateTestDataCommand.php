<?php

namespace App\Command;

use App\Entity\Client;
use App\Entity\ClientStatus;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-test-data',
    description: 'Create test data for VaultBoard',
)]
class CreateTestDataCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Creating test data for VaultBoard');

        // Create test clients
        $clients = $this->createClients();
        
        $io->success([
            'Test data created successfully!',
            "Created {$clients} clients"
        ]);

        return Command::SUCCESS;
    }

    private function createClients(): int
    {
        $clientsData = [
            ['John', 'Doe', '<EMAIL>', '******-0101'],
            ['Jane', 'Smith', '<EMAIL>', '******-0102'],
            ['Michael', 'Johnson', '<EMAIL>', '******-0103'],
            ['Emily', 'Brown', '<EMAIL>', '******-0104'],
            ['David', 'Wilson', '<EMAIL>', '******-0105'],
        ];

        $count = 0;
        foreach ($clientsData as $clientData) {
            $client = new Client();
            $client->setFirstName($clientData[0]);
            $client->setLastName($clientData[1]);
            $client->setEmail($clientData[2]);
            $client->setPhone($clientData[3]);
            $client->setStatus(ClientStatus::ACTIVE);
            
            $this->entityManager->persist($client);
            $count++;
        }

        $this->entityManager->flush();

        // Update client_id values
        $this->entityManager->getConnection()->executeStatement(
            'UPDATE clients SET client_id = id WHERE client_id IS NULL'
        );

        return $count;
    }
}
