<?php

namespace App\Command;

use App\Entity\Client;
use App\Entity\ClientStatus;
use App\Entity\Account;
use App\Entity\AccountStatus;
use App\Entity\AccountType;
use App\Entity\Card;
use App\Entity\CardStatus;
use App\Entity\Transaction;
use App\Entity\TransactionType;
use App\Entity\TransactionStatus;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-test-data',
    description: 'Create test data for VaultBoard',
)]
class CreateTestDataCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Creating test data for VaultBoard');

        // Create test clients
        $clients = $this->createClients();

        // Create accounts for clients
        $accounts = $this->createAccounts();

        // Create cards for accounts
        $cards = $this->createCards();

        // Create sample transactions
        $transactions = $this->createTransactions();

        $io->success([
            'Test data created successfully!',
            "Created {$clients} clients",
            "Created {$accounts} accounts",
            "Created {$cards} cards",
            "Created {$transactions} transactions"
        ]);

        return Command::SUCCESS;
    }

    private function createClients(): int
    {
        $clientsData = [
            ['John', 'Doe', '<EMAIL>', '+1-555-0101'],
            ['Jane', 'Smith', '<EMAIL>', '+1-555-0102'],
            ['Michael', 'Johnson', '<EMAIL>', '+1-555-0103'],
            ['Emily', 'Brown', '<EMAIL>', '+1-555-0104'],
            ['David', 'Wilson', '<EMAIL>', '+1-555-0105'],
        ];

        $count = 0;
        foreach ($clientsData as $clientData) {
            // Check if client already exists
            $existingClient = $this->entityManager->getRepository(Client::class)->findByEmail($clientData[2]);
            if ($existingClient) {
                continue; // Skip if already exists
            }

            $client = new Client();
            $client->setFirstName($clientData[0]);
            $client->setLastName($clientData[1]);
            $client->setEmail($clientData[2]);
            $client->setPhone($clientData[3]);
            $client->setStatus(ClientStatus::ACTIVE);

            $this->entityManager->persist($client);
            $count++;
        }

        if ($count > 0) {
            $this->entityManager->flush();

            // Update client_id values
            $this->entityManager->getConnection()->executeStatement(
                'UPDATE clients SET client_id = id WHERE client_id IS NULL'
            );
        }

        return $count;
    }

    private function createAccounts(): int
    {
        // Get existing clients
        $clients = $this->entityManager->getRepository(Client::class)->findAll();

        $count = 0;
        foreach ($clients as $client) {
            // Check if client already has an account
            if ($client->getActiveAccount()) {
                continue; // Skip if client already has an active account
            }

            $account = new Account();
            $account->setClient($client);
            $account->setAccountType(rand(0, 1) ? AccountType::CHECKING : AccountType::SAVINGS);
            $account->setBalance((string)(rand(100, 50000) / 100)); // Random balance between $1 and $500
            $account->setStatus(AccountStatus::OPEN);

            $this->entityManager->persist($account);
            $count++;
        }

        if ($count > 0) {
            $this->entityManager->flush();

            // Update account_id values
            $this->entityManager->getConnection()->executeStatement(
                'UPDATE accounts SET account_id = id WHERE account_id IS NULL'
            );
        }

        return $count;
    }

    private function createCards(): int
    {
        // Get existing accounts
        $accounts = $this->entityManager->getRepository(Account::class)->findAll();

        $count = 0;
        foreach ($accounts as $account) {
            // Check if account already has a card
            if ($account->getActiveCard()) {
                continue; // Skip if account already has an active card
            }

            $card = new Card();
            $card->setAccount($account);
            $card->setCardNumber($this->generateCardNumber());
            $card->setStatus(CardStatus::ACTIVE);

            $this->entityManager->persist($card);
            $count++;
        }

        if ($count > 0) {
            $this->entityManager->flush();

            // Update card_id values
            $this->entityManager->getConnection()->executeStatement(
                'UPDATE cards SET card_id = id WHERE card_id IS NULL'
            );
        }

        return $count;
    }

    private function createTransactions(): int
    {
        // Get existing accounts
        $accounts = $this->entityManager->getRepository(Account::class)->findAll();

        $count = 0;
        foreach ($accounts as $account) {
            // Create 3-5 transactions per account
            $transactionCount = rand(3, 5);

            for ($i = 0; $i < $transactionCount; $i++) {
                $transaction = new Transaction();
                $transaction->setAccount($account);

                // Random transaction type
                $types = [TransactionType::DEPOSIT, TransactionType::WITHDRAWAL];
                $type = $types[array_rand($types)];
                $transaction->setType($type);

                // Random amount
                $amount = (string)(rand(10, 1000) / 100); // $0.10 to $10.00
                $transaction->setAmount($amount);

                // Random timestamp within last 30 days
                $timestamp = new \DateTime('-' . rand(1, 30) . ' days');
                $transaction->setTimestamp($timestamp);

                $transaction->setStatus(TransactionStatus::SUCCESS);
                $transaction->setDescription($type === TransactionType::DEPOSIT ? 'Direct Deposit' : 'ATM Withdrawal');

                $this->entityManager->persist($transaction);
                $count++;
            }
        }

        $this->entityManager->flush();

        // Update transaction_id values
        $this->entityManager->getConnection()->executeStatement(
            'UPDATE transactions SET transaction_id = id WHERE transaction_id IS NULL'
        );

        return $count;
    }

    private function generateCardNumber(): string
    {
        // Generate a 16-digit card number starting with 4 (Visa-like)
        $cardNumber = '4';
        for ($i = 1; $i < 16; $i++) {
            $cardNumber .= rand(0, 9);
        }
        return $cardNumber;
    }
}
