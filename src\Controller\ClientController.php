<?php

namespace App\Controller;

use App\Entity\Client;
use App\Entity\ClientStatus;
use App\Repository\ClientRepository;
use App\Service\AuditLogService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/admin/clients')]
#[IsGranted('ROLE_ADMIN')]
class ClientController extends AbstractController
{
    public function __construct(
        private ClientRepository $clientRepository,
        private EntityManagerInterface $entityManager,
        private ValidatorInterface $validator,
        private AuditLogService $auditLogService
    ) {}

    #[Route('/', name: 'admin_clients_index')]
    public function index(Request $request): Response
    {
        $search = $request->query->get('search');
        $status = $request->query->get('status');

        if ($search) {
            $clients = $this->clientRepository->search($search);
        } elseif ($status) {
            $clientStatus = ClientStatus::tryFrom($status);
            $clients = $clientStatus ? $this->clientRepository->findByStatus($clientStatus) : [];
        } else {
            $clients = $this->clientRepository->findBy([], ['createdAt' => 'DESC']);
        }

        return $this->render('client/index.html.twig', [
            'clients' => $clients,
            'search' => $search,
            'current_status' => $status,
            'statuses' => ClientStatus::cases(),
        ]);
    }

    #[Route('/new', name: 'admin_clients_new')]
    #[IsGranted('client_create')]
    public function new(Request $request): Response
    {
        $client = new Client();

        if ($request->isMethod('POST')) {
            $client->setFirstName($request->request->get('first_name'));
            $client->setLastName($request->request->get('last_name'));
            $client->setEmail($request->request->get('email'));
            $client->setPhone($request->request->get('phone'));

            $statusValue = $request->request->get('status');
            if ($statusValue && ClientStatus::tryFrom($statusValue)) {
                $client->setStatus(ClientStatus::from($statusValue));
            }

            $errors = $this->validator->validate($client);

            if (count($errors) === 0) {
                $this->entityManager->persist($client);
                $this->entityManager->flush();

                // Log the action
                $this->auditLogService->log(
                    'Created Client',
                    'Client',
                    $client->getClientId(),
                    "Created client: {$client->getFullName()} ({$client->getEmail()})"
                );

                $this->addFlash('success', 'Client created successfully.');
                return $this->redirectToRoute('admin_clients_show', ['id' => $client->getId()]);
            }

            foreach ($errors as $error) {
                $this->addFlash('error', $error->getMessage());
            }
        }

        return $this->render('client/new.html.twig', [
            'client' => $client,
            'statuses' => ClientStatus::cases(),
        ]);
    }

    #[Route('/{id}', name: 'admin_clients_show', requirements: ['id' => '\d+'])]
    public function show(Client $client): Response
    {
        return $this->render('client/show.html.twig', [
            'client' => $client,
        ]);
    }

    #[Route('/{id}/edit', name: 'admin_clients_edit', requirements: ['id' => '\d+'])]
    #[IsGranted('client_update')]
    public function edit(Request $request, Client $client): Response
    {
        $originalData = [
            'firstName' => $client->getFirstName(),
            'lastName' => $client->getLastName(),
            'email' => $client->getEmail(),
            'phone' => $client->getPhone(),
            'status' => $client->getStatus(),
        ];

        if ($request->isMethod('POST')) {
            $client->setFirstName($request->request->get('first_name'));
            $client->setLastName($request->request->get('last_name'));
            $client->setEmail($request->request->get('email'));
            $client->setPhone($request->request->get('phone'));

            $statusValue = $request->request->get('status');
            if ($statusValue && ClientStatus::tryFrom($statusValue)) {
                $client->setStatus(ClientStatus::from($statusValue));
            }

            $errors = $this->validator->validate($client);

            if (count($errors) === 0) {
                $this->entityManager->flush();

                // Log the changes
                $changes = [];
                if ($originalData['firstName'] !== $client->getFirstName()) {
                    $changes[] = "First name: {$originalData['firstName']} → {$client->getFirstName()}";
                }
                if ($originalData['lastName'] !== $client->getLastName()) {
                    $changes[] = "Last name: {$originalData['lastName']} → {$client->getLastName()}";
                }
                if ($originalData['email'] !== $client->getEmail()) {
                    $changes[] = "Email: {$originalData['email']} → {$client->getEmail()}";
                }
                if ($originalData['phone'] !== $client->getPhone()) {
                    $changes[] = "Phone: {$originalData['phone']} → {$client->getPhone()}";
                }
                if ($originalData['status'] !== $client->getStatus()) {
                    $changes[] = "Status: {$originalData['status']->getLabel()} → {$client->getStatus()->getLabel()}";
                }

                if (!empty($changes)) {
                    $this->auditLogService->log(
                        'Updated Client',
                        'Client',
                        $client->getClientId(),
                        "Updated client {$client->getFullName()}: " . implode(', ', $changes)
                    );
                }

                $this->addFlash('success', 'Client updated successfully.');
                return $this->redirectToRoute('admin_clients_show', ['id' => $client->getId()]);
            }

            foreach ($errors as $error) {
                $this->addFlash('error', $error->getMessage());
            }
        }

        return $this->render('client/edit.html.twig', [
            'client' => $client,
            'statuses' => ClientStatus::cases(),
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_clients_delete', requirements: ['id' => '\d+'], methods: ['POST'])]
    #[IsGranted('client_delete')]
    public function delete(Request $request, Client $client): Response
    {
        if ($this->isCsrfTokenValid('delete'.$client->getId(), $request->request->get('_token'))) {
            // Check if client has active accounts
            if ($client->getActiveAccount()) {
                $this->addFlash('error', 'Cannot delete client with active accounts. Please close all accounts first.');
                return $this->redirectToRoute('admin_clients_show', ['id' => $client->getId()]);
            }

            $clientName = $client->getFullName();
            $clientId = $client->getClientId();

            $this->entityManager->remove($client);
            $this->entityManager->flush();

            // Log the action
            $this->auditLogService->log(
                'Deleted Client',
                'Client',
                $clientId,
                "Deleted client: {$clientName}"
            );

            $this->addFlash('success', 'Client deleted successfully.');
        }

        return $this->redirectToRoute('admin_clients_index');
    }

    #[Route('/{id}/toggle-status', name: 'admin_clients_toggle_status', requirements: ['id' => '\d+'], methods: ['POST'])]
    #[IsGranted('client_update')]
    public function toggleStatus(Request $request, Client $client): Response
    {
        if ($this->isCsrfTokenValid('toggle_status'.$client->getId(), $request->request->get('_token'))) {
            $oldStatus = $client->getStatus();

            // Toggle between active and inactive
            $newStatus = $oldStatus === ClientStatus::ACTIVE ? ClientStatus::INACTIVE : ClientStatus::ACTIVE;
            $client->setStatus($newStatus);

            $this->entityManager->flush();

            // Log the action
            $this->auditLogService->log(
                'Changed Client Status',
                'Client',
                $client->getClientId(),
                "Changed status of {$client->getFullName()} from {$oldStatus->getLabel()} to {$newStatus->getLabel()}"
            );

            $this->addFlash('success', "Client status changed to {$newStatus->getLabel()}.");
        }

        return $this->redirectToRoute('admin_clients_show', ['id' => $client->getId()]);
    }
}
