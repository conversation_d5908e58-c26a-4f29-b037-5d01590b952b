<?php

namespace App\Controller;

use App\Entity\Admin;
use App\Repository\ClientRepository;
use App\Repository\AccountRepository;
use App\Repository\CardRepository;
use App\Repository\TransactionRepository;
use App\Repository\AuditLogRepository;
use App\Repository\NotificationRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin')]
#[IsGranted('ROLE_ADMIN')]
class DashboardController extends AbstractController
{
    public function __construct(
        private ClientRepository $clientRepository,
        private AccountRepository $accountRepository,
        private CardRepository $cardRepository,
        private TransactionRepository $transactionRepository,
        private AuditLogRepository $auditLogRepository,
        private NotificationRepository $notificationRepository
    ) {}

    #[Route('/', name: 'admin_dashboard')]
    public function index(): Response
    {
        /** @var Admin $admin */
        $admin = $this->getUser();

        // Get dashboard statistics
        $stats = [
            'total_clients' => $this->clientRepository->getActiveClientsCount(),
            'total_accounts' => count($this->accountRepository->findByStatus(\App\Entity\AccountStatus::OPEN)),
            'total_balance' => $this->accountRepository->getTotalBalance(),
            'total_transactions' => count($this->transactionRepository->findByStatus(\App\Entity\TransactionStatus::SUCCESS)),
            'pending_transactions' => count($this->transactionRepository->getPendingTransactions()),
            'failed_transactions' => count($this->transactionRepository->getFailedTransactions()),
        ];

        // Get recent activity
        $recentClients = $this->clientRepository->getRecentClients(5);
        $recentAccounts = $this->accountRepository->getRecentAccounts(5);
        $recentTransactions = $this->transactionRepository->getRecentTransactions(10);
        $recentAuditLogs = $this->auditLogRepository->getRecentLogs(10);

        // Get notifications for current admin
        $notifications = $this->notificationRepository->findUnreadForAdmin($admin);

        // Get cards expiring soon
        $cardsExpiringSoon = $this->cardRepository->findCardsExpiringSoon(30);

        // Get low balance accounts
        $lowBalanceAccounts = $this->accountRepository->findLowBalanceAccounts('100.00');

        return $this->render('dashboard/index.html.twig', [
            'stats' => $stats,
            'recent_clients' => $recentClients,
            'recent_accounts' => $recentAccounts,
            'recent_transactions' => $recentTransactions,
            'recent_audit_logs' => $recentAuditLogs,
            'notifications' => $notifications,
            'cards_expiring_soon' => $cardsExpiringSoon,
            'low_balance_accounts' => $lowBalanceAccounts,
        ]);
    }

    #[Route('/reports', name: 'admin_reports')]
    public function reports(): Response
    {
        // Get various counts and statistics for reports
        $accountStats = $this->accountRepository->getCountByStatus();
        $cardStats = $this->cardRepository->getCountByStatus();
        $transactionStats = $this->transactionRepository->getCountByStatus();
        $transactionTypeStats = $this->transactionRepository->getCountByType();

        // Get date range for last 30 days
        $endDate = new \DateTime();
        $startDate = new \DateTime('-30 days');

        // Get daily transaction volume
        $dailyVolume = $this->transactionRepository->getDailyVolume($startDate, $endDate);

        // Get daily activity logs
        $dailyActivity = $this->auditLogRepository->getDailyActivity($startDate, $endDate);

        return $this->render('dashboard/reports.html.twig', [
            'account_stats' => $accountStats,
            'card_stats' => $cardStats,
            'transaction_stats' => $transactionStats,
            'transaction_type_stats' => $transactionTypeStats,
            'daily_volume' => $dailyVolume,
            'daily_activity' => $dailyActivity,
            'date_range' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
        ]);
    }

    #[Route('/profile', name: 'admin_profile')]
    public function profile(): Response
    {
        /** @var Admin $admin */
        $admin = $this->getUser();

        // Get admin's recent activity
        $recentActivity = $this->auditLogRepository->findByAdmin($admin, 20);

        // Get admin's notifications
        $notifications = $this->notificationRepository->findForAdmin($admin, 20);

        return $this->render('dashboard/profile.html.twig', [
            'admin' => $admin,
            'recent_activity' => $recentActivity,
            'notifications' => $notifications,
        ]);
    }
}
