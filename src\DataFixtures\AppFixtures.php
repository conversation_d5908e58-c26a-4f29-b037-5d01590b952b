<?php

namespace App\DataFixtures;

use App\Entity\Admin;
use App\Entity\AdminRole;
use App\Entity\AdminStatus;
use App\Entity\Client;
use App\Entity\ClientStatus;
use App\Entity\Account;
use App\Entity\AccountStatus;
use App\Entity\AccountType;
use App\Entity\Card;
use App\Entity\CardStatus;
use App\Entity\Transaction;
use App\Entity\TransactionType;
use App\Entity\TransactionStatus;
use App\Entity\Notification;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class AppFixtures extends Fixture
{
    public function __construct(
        private UserPasswordHasherInterface $passwordHasher
    ) {}

    public function load(ObjectManager $manager): void
    {
        // Create default admin users
        $this->createAdmins($manager);
        
        // Create sample clients
        $clients = $this->createClients($manager);
        
        // Create sample accounts
        $accounts = $this->createAccounts($manager, $clients);
        
        // Create sample cards
        $this->createCards($manager, $accounts);
        
        // Create sample transactions
        $this->createTransactions($manager, $accounts);
        
        // Create sample notifications
        $this->createNotifications($manager);

        $manager->flush();
    }

    private function createAdmins(ObjectManager $manager): void
    {
        // Super Admin
        $superAdmin = new Admin();
        $superAdmin->setUsername('admin');
        $superAdmin->setRole(AdminRole::SUPERADMIN);
        $superAdmin->setStatus(AdminStatus::ACTIVE);
        $hashedPassword = $this->passwordHasher->hashPassword($superAdmin, 'admin123');
        $superAdmin->setPassword($hashedPassword);
        $manager->persist($superAdmin);

        // Manager
        $manager1 = new Admin();
        $manager1->setUsername('manager');
        $manager1->setRole(AdminRole::MANAGER);
        $manager1->setStatus(AdminStatus::ACTIVE);
        $hashedPassword = $this->passwordHasher->hashPassword($manager1, 'manager123');
        $manager1->setPassword($hashedPassword);
        $manager->persist($manager1);

        // Auditor
        $auditor = new Admin();
        $auditor->setUsername('auditor');
        $auditor->setRole(AdminRole::AUDITOR);
        $auditor->setStatus(AdminStatus::ACTIVE);
        $hashedPassword = $this->passwordHasher->hashPassword($auditor, 'auditor123');
        $auditor->setPassword($hashedPassword);
        $manager->persist($auditor);
    }

    private function createClients(ObjectManager $manager): array
    {
        $clients = [];
        
        $clientsData = [
            ['John', 'Doe', '<EMAIL>', '******-0101', ClientStatus::ACTIVE],
            ['Jane', 'Smith', '<EMAIL>', '******-0102', ClientStatus::ACTIVE],
            ['Michael', 'Johnson', '<EMAIL>', '******-0103', ClientStatus::ACTIVE],
            ['Emily', 'Brown', '<EMAIL>', '******-0104', ClientStatus::ACTIVE],
            ['David', 'Wilson', '<EMAIL>', '******-0105', ClientStatus::INACTIVE],
            ['Sarah', 'Davis', '<EMAIL>', '******-0106', ClientStatus::ACTIVE],
            ['Robert', 'Miller', '<EMAIL>', '******-0107', ClientStatus::ACTIVE],
            ['Lisa', 'Garcia', '<EMAIL>', '******-0108', ClientStatus::BLOCKED],
        ];

        foreach ($clientsData as $index => $clientData) {
            $client = new Client();
            $client->setFirstName($clientData[0]);
            $client->setLastName($clientData[1]);
            $client->setEmail($clientData[2]);
            $client->setPhone($clientData[3]);
            $client->setStatus($clientData[4]);
            
            // Set creation date to simulate different registration times
            $createdAt = new \DateTime('-' . rand(1, 365) . ' days');
            $client->setCreatedAt($createdAt);
            
            $manager->persist($client);
            $clients[] = $client;
        }

        return $clients;
    }

    private function createAccounts(ObjectManager $manager, array $clients): array
    {
        $accounts = [];
        
        foreach ($clients as $index => $client) {
            // Skip some clients to simulate clients without accounts
            if ($index >= 6) continue;
            
            $account = new Account();
            $account->setClient($client);
            $account->setAccountType(rand(0, 1) ? AccountType::CHECKING : AccountType::SAVINGS);
            $account->setBalance((string)(rand(100, 50000) / 100)); // Random balance between $1 and $500
            
            // Most accounts are open, some are closed or frozen
            $statusRand = rand(1, 10);
            if ($statusRand <= 7) {
                $account->setStatus(AccountStatus::OPEN);
            } elseif ($statusRand <= 9) {
                $account->setStatus(AccountStatus::FROZEN);
            } else {
                $account->setStatus(AccountStatus::CLOSED);
                $account->setClosedAt(new \DateTime('-' . rand(1, 30) . ' days'));
            }
            
            // Set opening date
            $openedAt = new \DateTime('-' . rand(30, 365) . ' days');
            $account->setOpenedAt($openedAt);
            
            $manager->persist($account);
            $accounts[] = $account;
        }

        return $accounts;
    }

    private function createCards(ObjectManager $manager, array $accounts): void
    {
        foreach ($accounts as $account) {
            // Only create cards for open accounts
            if ($account->getStatus() !== AccountStatus::OPEN) continue;
            
            $card = new Card();
            $card->setAccount($account);
            $card->setCardNumber($this->generateCardNumber());
            
            // Set issue date
            $issuedDate = clone $account->getOpenedAt();
            $issuedDate->modify('+' . rand(1, 30) . ' days');
            $card->setIssuedDate($issuedDate);
            
            // Set expiry date (4 years from issue)
            $expiryDate = clone $issuedDate;
            $expiryDate->modify('+4 years');
            $card->setExpiryDate($expiryDate);
            
            // Most cards are active, some are expired or blocked
            $statusRand = rand(1, 10);
            if ($statusRand <= 8) {
                $card->setStatus(CardStatus::ACTIVE);
            } elseif ($statusRand <= 9) {
                $card->setStatus(CardStatus::EXPIRED);
            } else {
                $card->setStatus(CardStatus::BLOCKED);
            }
            
            $manager->persist($card);
        }
    }

    private function createTransactions(ObjectManager $manager, array $accounts): void
    {
        $openAccounts = array_filter($accounts, fn($account) => $account->getStatus() === AccountStatus::OPEN);
        
        foreach ($openAccounts as $account) {
            // Create 5-15 transactions per account
            $transactionCount = rand(5, 15);
            
            for ($i = 0; $i < $transactionCount; $i++) {
                $transaction = new Transaction();
                $transaction->setAccount($account);
                
                // Random transaction type
                $types = TransactionType::cases();
                $type = $types[array_rand($types)];
                $transaction->setType($type);
                
                // Random amount
                $amount = (string)(rand(10, 5000) / 100); // $0.10 to $50.00
                $transaction->setAmount($amount);
                
                // Random timestamp within last 90 days
                $timestamp = new \DateTime('-' . rand(1, 90) . ' days');
                $timestamp->modify('+' . rand(0, 23) . ' hours');
                $timestamp->modify('+' . rand(0, 59) . ' minutes');
                $transaction->setTimestamp($timestamp);
                
                // Most transactions are successful
                $statusRand = rand(1, 10);
                if ($statusRand <= 8) {
                    $transaction->setStatus(TransactionStatus::SUCCESS);
                } elseif ($statusRand <= 9) {
                    $transaction->setStatus(TransactionStatus::PENDING);
                } else {
                    $transaction->setStatus(TransactionStatus::FAILED);
                }
                
                // Add description
                $descriptions = [
                    'ATM Withdrawal',
                    'Online Purchase',
                    'Direct Deposit',
                    'Bill Payment',
                    'Transfer to Savings',
                    'Mobile Deposit',
                    'Wire Transfer',
                    'Check Deposit',
                ];
                $transaction->setDescription($descriptions[array_rand($descriptions)]);
                
                $manager->persist($transaction);
            }
        }
    }

    private function createNotifications(ObjectManager $manager): void
    {
        $notifications = [
            ['System maintenance scheduled for tonight at 2 AM EST', 'system'],
            ['New security update has been applied', 'security'],
            ['Monthly report is ready for review', 'info'],
            ['Low balance alert for multiple accounts', 'warning'],
            ['Backup completed successfully', 'success'],
        ];

        foreach ($notifications as $notificationData) {
            $notification = new Notification();
            $notification->setMessage($notificationData[0]);
            $notification->setType($notificationData[1]);
            $notification->setIsRead(rand(0, 1) === 1);
            
            // Set creation date within last 7 days
            $createdAt = new \DateTime('-' . rand(1, 7) . ' days');
            $notification->setCreatedAt($createdAt);
            
            $manager->persist($notification);
        }
    }

    private function generateCardNumber(): string
    {
        // Generate a 16-digit card number starting with 4 (Visa-like)
        $cardNumber = '4';
        for ($i = 1; $i < 16; $i++) {
            $cardNumber .= rand(0, 9);
        }
        return $cardNumber;
    }
}
