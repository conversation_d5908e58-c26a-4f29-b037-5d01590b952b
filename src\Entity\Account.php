<?php

namespace App\Entity;

use App\Repository\AccountRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: AccountRepository::class)]
#[ORM\Table(name: 'accounts')]
class Account
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(name: 'account_id', type: Types::INTEGER, unique: true)]
    #[ORM\GeneratedValue]
    private ?int $accountId = null;

    #[ORM\ManyToOne(inversedBy: 'accounts')]
    #[ORM\JoinColumn(name: 'client_id', referencedColumnName: 'client_id', nullable: false)]
    private ?Client $client = null;

    #[ORM\Column(name: 'account_type', type: Types::STRING, length: 20, enumType: AccountType::class)]
    private AccountType $accountType = AccountType::CHECKING;

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2)]
    #[Assert\PositiveOrZero]
    private ?string $balance = '0.00';

    #[ORM\Column(type: Types::STRING, length: 20, enumType: AccountStatus::class)]
    private AccountStatus $status = AccountStatus::OPEN;

    #[ORM\Column(name: 'opened_at', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $openedAt = null;

    #[ORM\Column(name: 'closed_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $closedAt = null;

    #[ORM\OneToMany(mappedBy: 'account', targetEntity: Card::class, orphanRemoval: true)]
    private Collection $cards;

    #[ORM\OneToMany(mappedBy: 'account', targetEntity: Transaction::class, orphanRemoval: true)]
    private Collection $transactions;

    #[ORM\OneToMany(mappedBy: 'targetAccount', targetEntity: Transaction::class)]
    private Collection $incomingTransactions;

    public function __construct()
    {
        $this->cards = new ArrayCollection();
        $this->transactions = new ArrayCollection();
        $this->incomingTransactions = new ArrayCollection();
        $this->openedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAccountId(): ?int
    {
        return $this->accountId;
    }

    public function setAccountId(int $accountId): static
    {
        $this->accountId = $accountId;
        return $this;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): static
    {
        $this->client = $client;
        return $this;
    }

    public function getAccountType(): AccountType
    {
        return $this->accountType;
    }

    public function setAccountType(AccountType $accountType): static
    {
        $this->accountType = $accountType;
        return $this;
    }

    public function getBalance(): ?string
    {
        return $this->balance;
    }

    public function setBalance(string $balance): static
    {
        $this->balance = $balance;
        return $this;
    }

    public function getFormattedBalance(): string
    {
        return '$' . number_format((float)$this->balance, 2);
    }

    public function getStatus(): AccountStatus
    {
        return $this->status;
    }

    public function setStatus(AccountStatus $status): static
    {
        $this->status = $status;
        
        // Set closed_at when account is closed
        if ($status === AccountStatus::CLOSED && $this->closedAt === null) {
            $this->closedAt = new \DateTime();
        }
        
        return $this;
    }

    public function getOpenedAt(): ?\DateTimeInterface
    {
        return $this->openedAt;
    }

    public function setOpenedAt(\DateTimeInterface $openedAt): static
    {
        $this->openedAt = $openedAt;
        return $this;
    }

    public function getClosedAt(): ?\DateTimeInterface
    {
        return $this->closedAt;
    }

    public function setClosedAt(?\DateTimeInterface $closedAt): static
    {
        $this->closedAt = $closedAt;
        return $this;
    }

    /**
     * @return Collection<int, Card>
     */
    public function getCards(): Collection
    {
        return $this->cards;
    }

    public function addCard(Card $card): static
    {
        if (!$this->cards->contains($card)) {
            $this->cards->add($card);
            $card->setAccount($this);
        }

        return $this;
    }

    public function removeCard(Card $card): static
    {
        if ($this->cards->removeElement($card)) {
            if ($card->getAccount() === $this) {
                $card->setAccount(null);
            }
        }

        return $this;
    }

    /**
     * Get the active card for this account (business rule: one active card per account)
     */
    public function getActiveCard(): ?Card
    {
        foreach ($this->cards as $card) {
            if ($card->getStatus() === CardStatus::ACTIVE) {
                return $card;
            }
        }
        return null;
    }

    /**
     * Check if account can have a new card (business rule enforcement)
     */
    public function canHaveNewCard(): bool
    {
        return $this->getActiveCard() === null && $this->status === AccountStatus::OPEN;
    }

    /**
     * @return Collection<int, Transaction>
     */
    public function getTransactions(): Collection
    {
        return $this->transactions;
    }

    public function addTransaction(Transaction $transaction): static
    {
        if (!$this->transactions->contains($transaction)) {
            $this->transactions->add($transaction);
            $transaction->setAccount($this);
        }

        return $this;
    }

    public function removeTransaction(Transaction $transaction): static
    {
        if ($this->transactions->removeElement($transaction)) {
            if ($transaction->getAccount() === $this) {
                $transaction->setAccount(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Transaction>
     */
    public function getIncomingTransactions(): Collection
    {
        return $this->incomingTransactions;
    }

    public function addIncomingTransaction(Transaction $transaction): static
    {
        if (!$this->incomingTransactions->contains($transaction)) {
            $this->incomingTransactions->add($transaction);
            $transaction->setTargetAccount($this);
        }

        return $this;
    }

    public function removeIncomingTransaction(Transaction $transaction): static
    {
        if ($this->incomingTransactions->removeElement($transaction)) {
            if ($transaction->getTargetAccount() === $this) {
                $transaction->setTargetAccount(null);
            }
        }

        return $this;
    }

    /**
     * Check if account is active (can perform transactions)
     */
    public function isActive(): bool
    {
        return $this->status === AccountStatus::OPEN;
    }

    /**
     * Generate account number for display
     */
    public function getAccountNumber(): string
    {
        return 'VB' . str_pad((string)$this->accountId, 8, '0', STR_PAD_LEFT);
    }
}
