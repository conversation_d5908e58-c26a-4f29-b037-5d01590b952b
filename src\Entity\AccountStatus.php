<?php

namespace App\Entity;

enum AccountStatus: string
{
    case OPEN = 'open';
    case CLOSED = 'closed';
    case FROZEN = 'frozen';

    public function getLabel(): string
    {
        return match($this) {
            self::OPEN => 'Open',
            self::CLOSED => 'Closed',
            self::FROZEN => 'Frozen',
        };
    }

    public function getBadgeClass(): string
    {
        return match($this) {
            self::OPEN => 'badge-success',
            self::CLOSED => 'badge-secondary',
            self::FROZEN => 'badge-warning',
        };
    }
}
