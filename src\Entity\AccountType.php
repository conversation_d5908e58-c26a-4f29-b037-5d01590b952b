<?php

namespace App\Entity;

enum AccountType: string
{
    case CHECKING = 'checking';
    case SAVINGS = 'savings';

    public function getLabel(): string
    {
        return match($this) {
            self::CHECKING => 'Checking',
            self::SAVINGS => 'Savings',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::CHECKING => 'fas fa-credit-card',
            self::SAVINGS => 'fas fa-piggy-bank',
        };
    }
}
