<?php

namespace App\Entity;

enum AdminRole: string
{
    case SUPERADMIN = 'superadmin';
    case MANAGER = 'manager';
    case AUDITOR = 'auditor';

    public function getLabel(): string
    {
        return match($this) {
            self::SUPERADMIN => 'Super Admin',
            self::MANAGER => 'Manager',
            self::AUDITOR => 'Auditor',
        };
    }

    public function getBadgeClass(): string
    {
        return match($this) {
            self::SUPERADMIN => 'badge-danger',
            self::MANAGER => 'badge-primary',
            self::AUDITOR => 'badge-info',
        };
    }

    /**
     * Get permissions for this role
     */
    public function getPermissions(): array
    {
        return match($this) {
            self::SUPERADMIN => [
                'client_create', 'client_read', 'client_update', 'client_delete',
                'account_create', 'account_read', 'account_update', 'account_delete',
                'card_create', 'card_read', 'card_update', 'card_delete',
                'transaction_create', 'transaction_read', 'transaction_update', 'transaction_delete',
                'admin_create', 'admin_read', 'admin_update', 'admin_delete',
                'audit_read', 'reports_read'
            ],
            self::MANAGER => [
                'client_create', 'client_read', 'client_update',
                'account_create', 'account_read', 'account_update',
                'card_create', 'card_read', 'card_update',
                'transaction_create', 'transaction_read', 'transaction_update',
                'audit_read', 'reports_read'
            ],
            self::AUDITOR => [
                'client_read', 'account_read', 'card_read', 'transaction_read',
                'audit_read', 'reports_read'
            ],
        };
    }

    /**
     * Check if role has specific permission
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissions());
    }
}
