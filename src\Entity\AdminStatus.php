<?php

namespace App\Entity;

enum AdminStatus: string
{
    case ACTIVE = 'active';
    case DISABLED = 'disabled';

    public function getLabel(): string
    {
        return match($this) {
            self::ACTIVE => 'Active',
            self::DISABLED => 'Disabled',
        };
    }

    public function getBadgeClass(): string
    {
        return match($this) {
            self::ACTIVE => 'badge-success',
            self::DISABLED => 'badge-secondary',
        };
    }
}
