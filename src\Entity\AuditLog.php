<?php

namespace App\Entity;

use App\Repository\AuditLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: AuditLogRepository::class)]
#[ORM\Table(name: 'audit_logs')]
class AuditLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(name: 'log_id', type: Types::INTEGER, unique: true)]
    #[ORM\GeneratedValue]
    private ?int $logId = null;

    #[ORM\ManyToOne(inversedBy: 'auditLogs')]
    #[ORM\JoinColumn(name: 'admin_id', referencedColumnName: 'id', nullable: false)]
    private ?Admin $admin = null;

    #[ORM\Column(name: 'action_type', type: Types::STRING, length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    private ?string $actionType = null;

    #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
    #[Assert\Length(max: 100)]
    private ?string $entity = null;

    #[ORM\Column(name: 'entity_id', type: Types::INTEGER, nullable: true)]
    private ?int $entityId = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $timestamp = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $details = null;

    #[ORM\Column(name: 'ip_address', type: Types::STRING, length: 45, nullable: true)]
    private ?string $ipAddress = null;

    #[ORM\Column(name: 'user_agent', type: Types::TEXT, nullable: true)]
    private ?string $userAgent = null;

    public function __construct()
    {
        $this->timestamp = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLogId(): ?int
    {
        return $this->logId;
    }

    public function setLogId(int $logId): static
    {
        $this->logId = $logId;
        return $this;
    }

    public function getAdmin(): ?Admin
    {
        return $this->admin;
    }

    public function setAdmin(?Admin $admin): static
    {
        $this->admin = $admin;
        return $this;
    }

    public function getActionType(): ?string
    {
        return $this->actionType;
    }

    public function setActionType(string $actionType): static
    {
        $this->actionType = $actionType;
        return $this;
    }

    public function getEntity(): ?string
    {
        return $this->entity;
    }

    public function setEntity(?string $entity): static
    {
        $this->entity = $entity;
        return $this;
    }

    public function getEntityId(): ?int
    {
        return $this->entityId;
    }

    public function setEntityId(?int $entityId): static
    {
        $this->entityId = $entityId;
        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): static
    {
        $this->timestamp = $timestamp;
        return $this;
    }

    public function getDetails(): ?string
    {
        return $this->details;
    }

    public function setDetails(?string $details): static
    {
        $this->details = $details;
        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): static
    {
        $this->ipAddress = $ipAddress;
        return $this;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function setUserAgent(?string $userAgent): static
    {
        $this->userAgent = $userAgent;
        return $this;
    }

    /**
     * Get formatted action description
     */
    public function getActionDescription(): string
    {
        $description = $this->actionType;

        if ($this->entity && $this->entityId) {
            $description .= " {$this->entity} (ID: {$this->entityId})";
        } elseif ($this->entity) {
            $description .= " {$this->entity}";
        }

        return $description;
    }

    /**
     * Get action severity level for styling
     */
    public function getSeverityLevel(): string
    {
        $action = strtolower($this->actionType);

        if (str_contains($action, 'delete') || str_contains($action, 'block') || str_contains($action, 'disable')) {
            return 'danger';
        }

        if (str_contains($action, 'create') || str_contains($action, 'add')) {
            return 'success';
        }

        if (str_contains($action, 'update') || str_contains($action, 'modify') || str_contains($action, 'edit')) {
            return 'warning';
        }

        return 'info';
    }

    /**
     * Get icon for action type
     */
    public function getActionIcon(): string
    {
        $action = strtolower($this->actionType);

        if (str_contains($action, 'create') || str_contains($action, 'add')) {
            return 'fas fa-plus-circle';
        }

        if (str_contains($action, 'update') || str_contains($action, 'modify') || str_contains($action, 'edit')) {
            return 'fas fa-edit';
        }

        if (str_contains($action, 'delete') || str_contains($action, 'remove')) {
            return 'fas fa-trash';
        }

        if (str_contains($action, 'view') || str_contains($action, 'read')) {
            return 'fas fa-eye';
        }

        if (str_contains($action, 'login')) {
            return 'fas fa-sign-in-alt';
        }

        if (str_contains($action, 'logout')) {
            return 'fas fa-sign-out-alt';
        }

        return 'fas fa-info-circle';
    }
}
