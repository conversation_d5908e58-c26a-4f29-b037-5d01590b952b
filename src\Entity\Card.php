<?php

namespace App\Entity;

use App\Repository\CardRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: CardRepository::class)]
#[ORM\Table(name: 'cards')]
class Card
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(name: 'card_id', type: Types::INTEGER, unique: true)]
    #[ORM\GeneratedValue]
    private ?int $cardId = null;

    #[ORM\ManyToOne(inversedBy: 'cards')]
    #[ORM\JoinColumn(name: 'account_id', referencedColumnName: 'id', nullable: false)]
    private ?Account $account = null;

    #[ORM\Column(name: 'card_number', type: Types::STRING, length: 16, unique: true)]
    #[Assert\NotBlank]
    #[Assert\Length(exactly: 16)]
    #[Assert\Regex(pattern: '/^\d{16}$/', message: 'Card number must be exactly 16 digits')]
    private ?string $cardNumber = null;

    #[ORM\Column(name: 'issued_date', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $issuedDate = null;

    #[ORM\Column(name: 'expiry_date', type: Types::DATE_MUTABLE)]
    #[Assert\NotBlank]
    #[Assert\GreaterThan('today')]
    private ?\DateTimeInterface $expiryDate = null;

    #[ORM\Column(type: Types::STRING, length: 20, enumType: CardStatus::class)]
    private CardStatus $status = CardStatus::ACTIVE;

    public function __construct()
    {
        $this->issuedDate = new \DateTime();
        // Set expiry date to 4 years from now by default
        $this->expiryDate = new \DateTime('+4 years');
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCardId(): ?int
    {
        return $this->cardId;
    }

    public function setCardId(int $cardId): static
    {
        $this->cardId = $cardId;
        return $this;
    }

    public function getAccount(): ?Account
    {
        return $this->account;
    }

    public function setAccount(?Account $account): static
    {
        $this->account = $account;
        return $this;
    }

    public function getCardNumber(): ?string
    {
        return $this->cardNumber;
    }

    public function setCardNumber(string $cardNumber): static
    {
        $this->cardNumber = $cardNumber;
        return $this;
    }

    /**
     * Get masked card number for display (e.g., ****-****-****-1234)
     */
    public function getMaskedCardNumber(): string
    {
        if (!$this->cardNumber) {
            return '';
        }

        $masked = str_repeat('*', 12) . substr($this->cardNumber, -4);
        return chunk_split($masked, 4, '-');
    }

    /**
     * Get formatted card number for display (e.g., 1234-5678-9012-3456)
     */
    public function getFormattedCardNumber(): string
    {
        if (!$this->cardNumber) {
            return '';
        }

        return chunk_split($this->cardNumber, 4, '-');
    }

    public function getIssuedDate(): ?\DateTimeInterface
    {
        return $this->issuedDate;
    }

    public function setIssuedDate(\DateTimeInterface $issuedDate): static
    {
        $this->issuedDate = $issuedDate;
        return $this;
    }

    public function getExpiryDate(): ?\DateTimeInterface
    {
        return $this->expiryDate;
    }

    public function setExpiryDate(\DateTimeInterface $expiryDate): static
    {
        $this->expiryDate = $expiryDate;
        return $this;
    }

    public function getStatus(): CardStatus
    {
        return $this->status;
    }

    public function setStatus(CardStatus $status): static
    {
        $this->status = $status;
        return $this;
    }

    /**
     * Check if card is expired
     */
    public function isExpired(): bool
    {
        return $this->expiryDate < new \DateTime();
    }

    /**
     * Check if card is active and not expired
     */
    public function isUsable(): bool
    {
        return $this->status === CardStatus::ACTIVE && !$this->isExpired();
    }

    /**
     * Generate a random 16-digit card number
     */
    public static function generateCardNumber(): string
    {
        // Generate a 16-digit number starting with 4 (Visa-like format)
        $cardNumber = '4';
        for ($i = 1; $i < 16; $i++) {
            $cardNumber .= random_int(0, 9);
        }
        return $cardNumber;
    }

    /**
     * Get card type based on first digit
     */
    public function getCardType(): string
    {
        if (!$this->cardNumber) {
            return 'Unknown';
        }

        $firstDigit = substr($this->cardNumber, 0, 1);
        return match($firstDigit) {
            '4' => 'Visa',
            '5' => 'Mastercard',
            '3' => 'American Express',
            default => 'VaultBoard Card'
        };
    }

    /**
     * Get expiry date in MM/YY format
     */
    public function getExpiryFormatted(): string
    {
        if (!$this->expiryDate) {
            return '';
        }

        return $this->expiryDate->format('m/y');
    }
}
