<?php

namespace App\Entity;

enum CardStatus: string
{
    case ACTIVE = 'active';
    case EXPIRED = 'expired';
    case LOST = 'lost';
    case BLOCKED = 'blocked';

    public function getLabel(): string
    {
        return match($this) {
            self::ACTIVE => 'Active',
            self::EXPIRED => 'Expired',
            self::LOST => 'Lost',
            self::BLOCKED => 'Blocked',
        };
    }

    public function getBadgeClass(): string
    {
        return match($this) {
            self::ACTIVE => 'badge-success',
            self::EXPIRED => 'badge-warning',
            self::LOST => 'badge-danger',
            self::BLOCKED => 'badge-danger',
        };
    }
}
