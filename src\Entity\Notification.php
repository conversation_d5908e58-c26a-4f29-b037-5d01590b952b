<?php

namespace App\Entity;

use App\Repository\NotificationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: NotificationRepository::class)]
#[ORM\Table(name: 'notifications')]
class Notification
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(name: 'notification_id', type: Types::INTEGER, unique: true)]
    #[ORM\GeneratedValue]
    private ?int $notificationId = null;

    #[ORM\ManyToOne(inversedBy: 'notifications')]
    #[ORM\JoinColumn(name: 'admin_id', referencedColumnName: 'id', nullable: true)]
    private ?Admin $admin = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    private ?string $message = null;

    #[ORM\Column(name: 'is_read', type: Types::BOOLEAN)]
    private bool $isRead = false;

    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
    private ?string $type = null;

    #[ORM\Column(name: 'related_entity', type: Types::STRING, length: 100, nullable: true)]
    private ?string $relatedEntity = null;

    #[ORM\Column(name: 'related_entity_id', type: Types::INTEGER, nullable: true)]
    private ?int $relatedEntityId = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNotificationId(): ?int
    {
        return $this->notificationId;
    }

    public function setNotificationId(int $notificationId): static
    {
        $this->notificationId = $notificationId;
        return $this;
    }

    public function getAdmin(): ?Admin
    {
        return $this->admin;
    }

    public function setAdmin(?Admin $admin): static
    {
        $this->admin = $admin;
        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): static
    {
        $this->message = $message;
        return $this;
    }

    public function isRead(): bool
    {
        return $this->isRead;
    }

    public function setIsRead(bool $isRead): static
    {
        $this->isRead = $isRead;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getRelatedEntity(): ?string
    {
        return $this->relatedEntity;
    }

    public function setRelatedEntity(?string $relatedEntity): static
    {
        $this->relatedEntity = $relatedEntity;
        return $this;
    }

    public function getRelatedEntityId(): ?int
    {
        return $this->relatedEntityId;
    }

    public function setRelatedEntityId(?int $relatedEntityId): static
    {
        $this->relatedEntityId = $relatedEntityId;
        return $this;
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): static
    {
        $this->isRead = true;
        return $this;
    }

    /**
     * Mark notification as unread
     */
    public function markAsUnread(): static
    {
        $this->isRead = false;
        return $this;
    }

    /**
     * Get notification icon based on type
     */
    public function getIcon(): string
    {
        return match($this->type) {
            'success' => 'fas fa-check-circle text-success',
            'warning' => 'fas fa-exclamation-triangle text-warning',
            'error' => 'fas fa-times-circle text-danger',
            'info' => 'fas fa-info-circle text-info',
            'security' => 'fas fa-shield-alt text-warning',
            'system' => 'fas fa-cog text-secondary',
            default => 'fas fa-bell text-primary'
        };
    }

    /**
     * Get notification badge class based on type
     */
    public function getBadgeClass(): string
    {
        return match($this->type) {
            'success' => 'badge-success',
            'warning' => 'badge-warning',
            'error' => 'badge-danger',
            'info' => 'badge-info',
            'security' => 'badge-warning',
            'system' => 'badge-secondary',
            default => 'badge-primary'
        };
    }

    /**
     * Get time ago string
     */
    public function getTimeAgo(): string
    {
        $now = new \DateTime();
        $diff = $now->diff($this->createdAt);

        if ($diff->days > 0) {
            return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
        }

        if ($diff->h > 0) {
            return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
        }

        if ($diff->i > 0) {
            return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
        }

        return 'Just now';
    }
}
