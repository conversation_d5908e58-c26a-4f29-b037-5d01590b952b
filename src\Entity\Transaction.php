<?php

namespace App\Entity;

use App\Repository\TransactionRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: TransactionRepository::class)]
#[ORM\Table(name: 'transactions')]
class Transaction
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(name: 'transaction_id', type: Types::INTEGER, unique: true)]
    #[ORM\GeneratedValue]
    private ?int $transactionId = null;

    #[ORM\ManyToOne(inversedBy: 'transactions')]
    #[ORM\JoinColumn(name: 'account_id', referencedColumnName: 'id', nullable: false)]
    private ?Account $account = null;

    #[ORM\Column(type: Types::STRING, length: 20, enumType: TransactionType::class)]
    private TransactionType $type = TransactionType::DEPOSIT;

    #[ORM\Column(type: Types::DECIMAL, precision: 12, scale: 2)]
    #[Assert\Positive]
    private ?string $amount = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $timestamp = null;

    #[ORM\Column(type: Types::STRING, length: 20, enumType: TransactionStatus::class)]
    private TransactionStatus $status = TransactionStatus::SUCCESS;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne(inversedBy: 'incomingTransactions')]
    #[ORM\JoinColumn(name: 'target_account_id', referencedColumnName: 'id', nullable: true)]
    private ?Account $targetAccount = null;

    public function __construct()
    {
        $this->timestamp = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTransactionId(): ?int
    {
        return $this->transactionId;
    }

    public function setTransactionId(int $transactionId): static
    {
        $this->transactionId = $transactionId;
        return $this;
    }

    public function getAccount(): ?Account
    {
        return $this->account;
    }

    public function setAccount(?Account $account): static
    {
        $this->account = $account;
        return $this;
    }

    public function getType(): TransactionType
    {
        return $this->type;
    }

    public function setType(TransactionType $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): static
    {
        $this->amount = $amount;
        return $this;
    }

    public function getFormattedAmount(): string
    {
        $prefix = $this->type->isDebit() ? '-' : '+';
        return $prefix . '$' . number_format((float)$this->amount, 2);
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): static
    {
        $this->timestamp = $timestamp;
        return $this;
    }

    public function getStatus(): TransactionStatus
    {
        return $this->status;
    }

    public function setStatus(TransactionStatus $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function getTargetAccount(): ?Account
    {
        return $this->targetAccount;
    }

    public function setTargetAccount(?Account $targetAccount): static
    {
        $this->targetAccount = $targetAccount;
        return $this;
    }

    /**
     * Generate transaction reference number
     */
    public function getReference(): string
    {
        return 'TXN' . str_pad((string)$this->transactionId, 10, '0', STR_PAD_LEFT);
    }

    /**
     * Get transaction summary for display
     */
    public function getSummary(): string
    {
        $summary = $this->type->getLabel() . ' of ' . $this->getFormattedAmount();

        if ($this->targetAccount && in_array($this->type, [TransactionType::TRANSFER_OUT, TransactionType::TRANSFER_IN])) {
            $direction = $this->type === TransactionType::TRANSFER_OUT ? 'to' : 'from';
            $summary .= ' ' . $direction . ' Account ' . $this->targetAccount->getAccountNumber();
        }

        return $summary;
    }

    /**
     * Check if transaction is successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === TransactionStatus::SUCCESS;
    }

    /**
     * Check if transaction is pending
     */
    public function isPending(): bool
    {
        return $this->status === TransactionStatus::PENDING;
    }

    /**
     * Check if transaction failed
     */
    public function isFailed(): bool
    {
        return $this->status === TransactionStatus::FAILED;
    }
}
