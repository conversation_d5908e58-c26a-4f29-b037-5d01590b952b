<?php

namespace App\Entity;

enum TransactionStatus: string
{
    case SUCCESS = 'success';
    case FAILED = 'failed';
    case PENDING = 'pending';

    public function getLabel(): string
    {
        return match($this) {
            self::SUCCESS => 'Success',
            self::FAILED => 'Failed',
            self::PENDING => 'Pending',
        };
    }

    public function getBadgeClass(): string
    {
        return match($this) {
            self::SUCCESS => 'badge-success',
            self::FAILED => 'badge-danger',
            self::PENDING => 'badge-warning',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::SUCCESS => 'fas fa-check-circle text-success',
            self::FAILED => 'fas fa-times-circle text-danger',
            self::PENDING => 'fas fa-clock text-warning',
        };
    }
}
