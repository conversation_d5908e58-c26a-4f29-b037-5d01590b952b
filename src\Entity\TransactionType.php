<?php

namespace App\Entity;

enum TransactionType: string
{
    case DEPOSIT = 'deposit';
    case WITHDRAWAL = 'withdrawal';
    case TRANSFER_OUT = 'transfer_out';
    case TRANSFER_IN = 'transfer_in';

    public function getLabel(): string
    {
        return match($this) {
            self::DEPOSIT => 'Deposit',
            self::WITHDRAWAL => 'Withdrawal',
            self::TRANSFER_OUT => 'Transfer Out',
            self::TRANSFER_IN => 'Transfer In',
        };
    }

    public function getIcon(): string
    {
        return match($this) {
            self::DEPOSIT => 'fas fa-arrow-down text-success',
            self::WITHDRAWAL => 'fas fa-arrow-up text-danger',
            self::TRANSFER_OUT => 'fas fa-arrow-right text-warning',
            self::TRANSFER_IN => 'fas fa-arrow-left text-info',
        };
    }

    public function getBadgeClass(): string
    {
        return match($this) {
            self::DEPOSIT => 'badge-success',
            self::WITHDRAWAL => 'badge-danger',
            self::TRANSFER_OUT => 'badge-warning',
            self::TRANSFER_IN => 'badge-info',
        };
    }

    /**
     * Check if this transaction type affects balance positively
     */
    public function isCredit(): bool
    {
        return in_array($this, [self::DEPOSIT, self::TRANSFER_IN]);
    }

    /**
     * Check if this transaction type affects balance negatively
     */
    public function isDebit(): bool
    {
        return in_array($this, [self::WITHDRAWAL, self::TRANSFER_OUT]);
    }
}
