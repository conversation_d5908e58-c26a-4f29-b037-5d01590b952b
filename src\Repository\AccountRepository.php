<?php

namespace App\Repository;

use App\Entity\Account;
use App\Entity\AccountStatus;
use App\Entity\AccountType;
use App\Entity\Client;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Account>
 */
class AccountRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Account::class);
    }

    public function save(Account $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Account $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find accounts by status
     */
    public function findByStatus(AccountStatus $status): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.status = :status')
            ->setParameter('status', $status)
            ->orderBy('a.openedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find active account for client (business rule: one active account per client)
     */
    public function findActiveAccountForClient(Client $client): ?Account
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.client = :client')
            ->andWhere('a.status = :status')
            ->setParameter('client', $client)
            ->setParameter('status', AccountStatus::OPEN)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Get total balance across all active accounts
     */
    public function getTotalBalance(): string
    {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.balance)')
            ->andWhere('a.status = :status')
            ->setParameter('status', AccountStatus::OPEN)
            ->getQuery()
            ->getSingleScalarResult();

        return $result ?? '0.00';
    }

    /**
     * Get accounts count by status
     */
    public function getCountByStatus(): array
    {
        $result = $this->createQueryBuilder('a')
            ->select('a.status, COUNT(a.id) as count')
            ->groupBy('a.status')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach (AccountStatus::cases() as $status) {
            $counts[$status->value] = 0;
        }

        foreach ($result as $row) {
            $counts[$row['status']->value] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Get accounts by type
     */
    public function findByType(AccountType $type): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.accountType = :type')
            ->setParameter('type', $type)
            ->orderBy('a.openedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get recent accounts (last 30 days)
     */
    public function getRecentAccounts(int $limit = 10): array
    {
        $thirtyDaysAgo = new \DateTime('-30 days');
        
        return $this->createQueryBuilder('a')
            ->andWhere('a.openedAt >= :date')
            ->setParameter('date', $thirtyDaysAgo)
            ->orderBy('a.openedAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Search accounts by account number or client name
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('a')
            ->leftJoin('a.client', 'c')
            ->andWhere('a.accountId LIKE :query OR c.firstName LIKE :query OR c.lastName LIKE :query OR c.email LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('a.openedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get accounts with low balance (less than specified amount)
     */
    public function findLowBalanceAccounts(string $threshold = '100.00'): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.balance < :threshold')
            ->andWhere('a.status = :status')
            ->setParameter('threshold', $threshold)
            ->setParameter('status', AccountStatus::OPEN)
            ->orderBy('a.balance', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get accounts opened in date range
     */
    public function findAccountsOpenedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.openedAt BETWEEN :start AND :end')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->orderBy('a.openedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
