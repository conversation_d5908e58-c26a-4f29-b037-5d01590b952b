<?php

namespace App\Repository;

use App\Entity\Admin;
use App\Entity\AdminRole;
use App\Entity\AdminStatus;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

/**
 * @extends ServiceEntityRepository<Admin>
 */
class AdminRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Admin::class);
    }

    public function save(Admin $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Admin $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof Admin) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

    /**
     * Find admin by username
     */
    public function findByUsername(string $username): ?Admin
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.username = :username')
            ->setParameter('username', $username)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find active admins
     */
    public function findActiveAdmins(): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.status = :status')
            ->setParameter('status', AdminStatus::ACTIVE)
            ->orderBy('a.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find admins by role
     */
    public function findByRole(AdminRole $role): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.role = :role')
            ->setParameter('role', $role)
            ->orderBy('a.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find admins by status
     */
    public function findByStatus(AdminStatus $status): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.status = :status')
            ->setParameter('status', $status)
            ->orderBy('a.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get admins count by role
     */
    public function getCountByRole(): array
    {
        $result = $this->createQueryBuilder('a')
            ->select('a.role, COUNT(a.id) as count')
            ->groupBy('a.role')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach (AdminRole::cases() as $role) {
            $counts[$role->value] = 0;
        }

        foreach ($result as $row) {
            $counts[$row['role']->value] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Get admins count by status
     */
    public function getCountByStatus(): array
    {
        $result = $this->createQueryBuilder('a')
            ->select('a.status, COUNT(a.id) as count')
            ->groupBy('a.status')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach (AdminStatus::cases() as $status) {
            $counts[$status->value] = 0;
        }

        foreach ($result as $row) {
            $counts[$row['status']->value] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Search admins by username
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.username LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('a.username', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get recent admins (last 30 days)
     */
    public function getRecentAdmins(int $limit = 10): array
    {
        $thirtyDaysAgo = new \DateTime('-30 days');
        
        return $this->createQueryBuilder('a')
            ->andWhere('a.createdAt >= :date')
            ->setParameter('date', $thirtyDaysAgo)
            ->orderBy('a.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Check if username exists
     */
    public function usernameExists(string $username): bool
    {
        $count = $this->createQueryBuilder('a')
            ->select('COUNT(a.id)')
            ->andWhere('a.username = :username')
            ->setParameter('username', $username)
            ->getQuery()
            ->getSingleScalarResult();

        return $count > 0;
    }
}
