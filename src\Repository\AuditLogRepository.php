<?php

namespace App\Repository;

use App\Entity\AuditLog;
use App\Entity\Admin;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AuditLog>
 */
class AuditLogRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AuditLog::class);
    }

    public function save(AuditLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AuditLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find logs by admin
     */
    public function findByAdmin(Admin $admin, int $limit = 50): array
    {
        return $this->createQueryBuilder('al')
            ->andWhere('al.admin = :admin')
            ->setParameter('admin', $admin)
            ->orderBy('al.timestamp', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find logs by action type
     */
    public function findByActionType(string $actionType): array
    {
        return $this->createQueryBuilder('al')
            ->andWhere('al.actionType = :actionType')
            ->setParameter('actionType', $actionType)
            ->orderBy('al.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find logs by entity
     */
    public function findByEntity(string $entity, ?int $entityId = null): array
    {
        $qb = $this->createQueryBuilder('al')
            ->andWhere('al.entity = :entity')
            ->setParameter('entity', $entity);

        if ($entityId !== null) {
            $qb->andWhere('al.entityId = :entityId')
               ->setParameter('entityId', $entityId);
        }

        return $qb->orderBy('al.timestamp', 'DESC')
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Search logs
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('al')
            ->leftJoin('al.admin', 'a')
            ->andWhere('al.actionType LIKE :query OR al.entity LIKE :query OR al.details LIKE :query OR a.username LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('al.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get recent logs
     */
    public function getRecentLogs(int $limit = 50): array
    {
        return $this->createQueryBuilder('al')
            ->orderBy('al.timestamp', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get logs in date range
     */
    public function findLogsBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('al')
            ->andWhere('al.timestamp BETWEEN :start AND :end')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->orderBy('al.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get activity summary by admin
     */
    public function getActivitySummaryByAdmin(): array
    {
        return $this->createQueryBuilder('al')
            ->select('a.username, COUNT(al.id) as activity_count')
            ->leftJoin('al.admin', 'a')
            ->groupBy('a.id')
            ->orderBy('activity_count', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get activity summary by action type
     */
    public function getActivitySummaryByAction(): array
    {
        return $this->createQueryBuilder('al')
            ->select('al.actionType, COUNT(al.id) as count')
            ->groupBy('al.actionType')
            ->orderBy('count', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get daily activity for date range
     */
    public function getDailyActivity(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = '
            SELECT DATE(timestamp) as date, COUNT(id) as count
            FROM audit_logs
            WHERE timestamp BETWEEN ? AND ?
            GROUP BY DATE(timestamp)
            ORDER BY date ASC
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery([
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        return $result->fetchAllAssociative();
    }

    /**
     * Clean old logs (older than specified days)
     */
    public function cleanOldLogs(int $daysToKeep = 365): int
    {
        $cutoffDate = new \DateTime("-{$daysToKeep} days");

        return $this->createQueryBuilder('al')
            ->delete()
            ->andWhere('al.timestamp < :cutoffDate')
            ->setParameter('cutoffDate', $cutoffDate)
            ->getQuery()
            ->execute();
    }
}
