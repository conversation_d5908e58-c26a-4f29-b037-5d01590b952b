<?php

namespace App\Repository;

use App\Entity\Card;
use App\Entity\CardStatus;
use App\Entity\Account;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Card>
 */
class CardRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Card::class);
    }

    public function save(Card $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Card $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find active card for account (business rule: one active card per account)
     */
    public function findActiveCardForAccount(Account $account): ?Card
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.account = :account')
            ->andWhere('c.status = :status')
            ->setParameter('account', $account)
            ->setParameter('status', CardStatus::ACTIVE)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find cards by status
     */
    public function findByStatus(CardStatus $status): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.status = :status')
            ->setParameter('status', $status)
            ->orderBy('c.issuedDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find expired cards
     */
    public function findExpiredCards(): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.expiryDate < :today')
            ->andWhere('c.status = :status')
            ->setParameter('today', new \DateTime())
            ->setParameter('status', CardStatus::ACTIVE)
            ->orderBy('c.expiryDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find cards expiring soon (within specified days)
     */
    public function findCardsExpiringSoon(int $days = 30): array
    {
        $futureDate = new \DateTime("+{$days} days");
        
        return $this->createQueryBuilder('c')
            ->andWhere('c.expiryDate BETWEEN :today AND :futureDate')
            ->andWhere('c.status = :status')
            ->setParameter('today', new \DateTime())
            ->setParameter('futureDate', $futureDate)
            ->setParameter('status', CardStatus::ACTIVE)
            ->orderBy('c.expiryDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get cards count by status
     */
    public function getCountByStatus(): array
    {
        $result = $this->createQueryBuilder('c')
            ->select('c.status, COUNT(c.id) as count')
            ->groupBy('c.status')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach (CardStatus::cases() as $status) {
            $counts[$status->value] = 0;
        }

        foreach ($result as $row) {
            $counts[$row['status']->value] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Search cards by card number or account details
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('c')
            ->leftJoin('c.account', 'a')
            ->leftJoin('a.client', 'cl')
            ->andWhere('c.cardNumber LIKE :query OR a.accountId LIKE :query OR cl.firstName LIKE :query OR cl.lastName LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('c.issuedDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Check if card number already exists
     */
    public function cardNumberExists(string $cardNumber): bool
    {
        $count = $this->createQueryBuilder('c')
            ->select('COUNT(c.id)')
            ->andWhere('c.cardNumber = :cardNumber')
            ->setParameter('cardNumber', $cardNumber)
            ->getQuery()
            ->getSingleScalarResult();

        return $count > 0;
    }

    /**
     * Generate unique card number
     */
    public function generateUniqueCardNumber(): string
    {
        do {
            $cardNumber = Card::generateCardNumber();
        } while ($this->cardNumberExists($cardNumber));

        return $cardNumber;
    }

    /**
     * Get recent cards (last 30 days)
     */
    public function getRecentCards(int $limit = 10): array
    {
        $thirtyDaysAgo = new \DateTime('-30 days');
        
        return $this->createQueryBuilder('c')
            ->andWhere('c.issuedDate >= :date')
            ->setParameter('date', $thirtyDaysAgo)
            ->orderBy('c.issuedDate', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get cards issued in date range
     */
    public function findCardsIssuedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.issuedDate BETWEEN :start AND :end')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->orderBy('c.issuedDate', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
