<?php

namespace App\Repository;

use App\Entity\Client;
use App\Entity\ClientStatus;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Client>
 */
class ClientRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Client::class);
    }

    public function save(Client $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Client $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find clients by status
     */
    public function findByStatus(ClientStatus $status): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.status = :status')
            ->setParameter('status', $status)
            ->orderBy('c.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search clients by name or email
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.firstName LIKE :query OR c.lastName LIKE :query OR c.email LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('c.firstName', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get active clients count
     */
    public function getActiveClientsCount(): int
    {
        return $this->createQueryBuilder('c')
            ->select('COUNT(c.id)')
            ->andWhere('c.status = :status')
            ->setParameter('status', ClientStatus::ACTIVE)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get clients with active accounts
     */
    public function findClientsWithActiveAccounts(): array
    {
        return $this->createQueryBuilder('c')
            ->innerJoin('c.accounts', 'a')
            ->andWhere('a.status = :accountStatus')
            ->andWhere('c.status = :clientStatus')
            ->setParameter('accountStatus', 'open')
            ->setParameter('clientStatus', ClientStatus::ACTIVE)
            ->orderBy('c.firstName', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get recent clients (last 30 days)
     */
    public function getRecentClients(int $limit = 10): array
    {
        $thirtyDaysAgo = new \DateTime('-30 days');
        
        return $this->createQueryBuilder('c')
            ->andWhere('c.createdAt >= :date')
            ->setParameter('date', $thirtyDaysAgo)
            ->orderBy('c.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find client by email
     */
    public function findByEmail(string $email): ?Client
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.email = :email')
            ->setParameter('email', $email)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
