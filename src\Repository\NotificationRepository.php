<?php

namespace App\Repository;

use App\Entity\Notification;
use App\Entity\Admin;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Notification>
 */
class NotificationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Notification::class);
    }

    public function save(Notification $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Notification $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find notifications for admin
     */
    public function findForAdmin(Admin $admin, int $limit = 20): array
    {
        return $this->createQueryBuilder('n')
            ->andWhere('n.admin = :admin OR n.admin IS NULL')
            ->setParameter('admin', $admin)
            ->orderBy('n.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find unread notifications for admin
     */
    public function findUnreadForAdmin(Admin $admin): array
    {
        return $this->createQueryBuilder('n')
            ->andWhere('n.admin = :admin OR n.admin IS NULL')
            ->andWhere('n.isRead = :isRead')
            ->setParameter('admin', $admin)
            ->setParameter('isRead', false)
            ->orderBy('n.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get unread count for admin
     */
    public function getUnreadCountForAdmin(Admin $admin): int
    {
        return $this->createQueryBuilder('n')
            ->select('COUNT(n.id)')
            ->andWhere('n.admin = :admin OR n.admin IS NULL')
            ->andWhere('n.isRead = :isRead')
            ->setParameter('admin', $admin)
            ->setParameter('isRead', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Find notifications by type
     */
    public function findByType(string $type): array
    {
        return $this->createQueryBuilder('n')
            ->andWhere('n.type = :type')
            ->setParameter('type', $type)
            ->orderBy('n.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find global notifications (not assigned to specific admin)
     */
    public function findGlobalNotifications(): array
    {
        return $this->createQueryBuilder('n')
            ->andWhere('n.admin IS NULL')
            ->orderBy('n.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Mark all notifications as read for admin
     */
    public function markAllAsReadForAdmin(Admin $admin): int
    {
        return $this->createQueryBuilder('n')
            ->update()
            ->set('n.isRead', ':isRead')
            ->andWhere('n.admin = :admin OR n.admin IS NULL')
            ->andWhere('n.isRead = :currentRead')
            ->setParameter('isRead', true)
            ->setParameter('admin', $admin)
            ->setParameter('currentRead', false)
            ->getQuery()
            ->execute();
    }

    /**
     * Get recent notifications
     */
    public function getRecentNotifications(int $limit = 20): array
    {
        return $this->createQueryBuilder('n')
            ->orderBy('n.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Clean old read notifications (older than specified days)
     */
    public function cleanOldReadNotifications(int $daysToKeep = 30): int
    {
        $cutoffDate = new \DateTime("-{$daysToKeep} days");
        
        return $this->createQueryBuilder('n')
            ->delete()
            ->andWhere('n.createdAt < :cutoffDate')
            ->andWhere('n.isRead = :isRead')
            ->setParameter('cutoffDate', $cutoffDate)
            ->setParameter('isRead', true)
            ->getQuery()
            ->execute();
    }

    /**
     * Search notifications
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('n')
            ->andWhere('n.message LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('n.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get notifications in date range
     */
    public function findNotificationsBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('n')
            ->andWhere('n.createdAt BETWEEN :start AND :end')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->orderBy('n.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
