<?php

namespace App\Repository;

use App\Entity\Transaction;
use App\Entity\TransactionStatus;
use App\Entity\TransactionType;
use App\Entity\Account;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Transaction>
 */
class TransactionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Transaction::class);
    }

    public function save(Transaction $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Transaction $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find transactions by account
     */
    public function findByAccount(Account $account, int $limit = 50): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.account = :account OR t.targetAccount = :account')
            ->setParameter('account', $account)
            ->orderBy('t.timestamp', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find transactions by status
     */
    public function findByStatus(TransactionStatus $status): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.status = :status')
            ->setParameter('status', $status)
            ->orderBy('t.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find transactions by type
     */
    public function findByType(TransactionType $type): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.type = :type')
            ->setParameter('type', $type)
            ->orderBy('t.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get transactions count by status
     */
    public function getCountByStatus(): array
    {
        $result = $this->createQueryBuilder('t')
            ->select('t.status, COUNT(t.id) as count')
            ->groupBy('t.status')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach (TransactionStatus::cases() as $status) {
            $counts[$status->value] = 0;
        }

        foreach ($result as $row) {
            $counts[$row['status']->value] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Get transactions count by type
     */
    public function getCountByType(): array
    {
        $result = $this->createQueryBuilder('t')
            ->select('t.type, COUNT(t.id) as count')
            ->groupBy('t.type')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach (TransactionType::cases() as $type) {
            $counts[$type->value] = 0;
        }

        foreach ($result as $row) {
            $counts[$row['type']->value] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Get total transaction volume
     */
    public function getTotalVolume(): string
    {
        $result = $this->createQueryBuilder('t')
            ->select('SUM(t.amount)')
            ->andWhere('t.status = :status')
            ->setParameter('status', TransactionStatus::SUCCESS)
            ->getQuery()
            ->getSingleScalarResult();

        return $result ?? '0.00';
    }

    /**
     * Get daily transaction volume for date range
     */
    public function getDailyVolume(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = '
            SELECT DATE(timestamp) as date, SUM(amount) as volume, COUNT(id) as count
            FROM transactions
            WHERE timestamp BETWEEN ? AND ?
            AND status = ?
            GROUP BY DATE(timestamp)
            ORDER BY date ASC
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery([
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s'),
            TransactionStatus::SUCCESS->value
        ]);

        return $result->fetchAllAssociative();
    }

    /**
     * Search transactions
     */
    public function search(string $query): array
    {
        return $this->createQueryBuilder('t')
            ->leftJoin('t.account', 'a')
            ->leftJoin('a.client', 'c')
            ->andWhere('t.transactionId LIKE :query OR t.description LIKE :query OR c.firstName LIKE :query OR c.lastName LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('t.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get recent transactions
     */
    public function getRecentTransactions(int $limit = 20): array
    {
        return $this->createQueryBuilder('t')
            ->orderBy('t.timestamp', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get transactions in date range
     */
    public function findTransactionsBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.timestamp BETWEEN :start AND :end')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->orderBy('t.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get pending transactions
     */
    public function getPendingTransactions(): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.status = :status')
            ->setParameter('status', TransactionStatus::PENDING)
            ->orderBy('t.timestamp', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get failed transactions
     */
    public function getFailedTransactions(): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.status = :status')
            ->setParameter('status', TransactionStatus::FAILED)
            ->orderBy('t.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get large transactions (above threshold)
     */
    public function getLargeTransactions(string $threshold = '10000.00'): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.amount >= :threshold')
            ->andWhere('t.status = :status')
            ->setParameter('threshold', $threshold)
            ->setParameter('status', TransactionStatus::SUCCESS)
            ->orderBy('t.amount', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
