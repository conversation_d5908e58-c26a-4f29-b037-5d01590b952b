<?php

namespace App\Security;

use App\Entity\Admin;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class AdminPermissionVoter extends Voter
{
    protected function supports(string $attribute, mixed $subject): bool
    {
        // Check if the attribute is a permission we handle
        $permissions = [
            'client_create', 'client_read', 'client_update', 'client_delete',
            'account_create', 'account_read', 'account_update', 'account_delete',
            'card_create', 'card_read', 'card_update', 'card_delete',
            'transaction_create', 'transaction_read', 'transaction_update', 'transaction_delete',
            'admin_create', 'admin_read', 'admin_update', 'admin_delete',
            'audit_read', 'reports_read'
        ];

        return in_array($attribute, $permissions);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        // If the user is not an Admin, deny access
        if (!$user instanceof Admin) {
            return false;
        }

        // Check if the admin has the required permission
        return $user->hasPermission($attribute);
    }
}
