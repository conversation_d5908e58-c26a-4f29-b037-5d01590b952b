<?php

namespace App\Service;

use App\Entity\Admin;
use App\Entity\AuditLog;
use App\Repository\AuditLogRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\RequestStack;

class AuditLogService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AuditLogRepository $auditLogRepository,
        private Security $security,
        private RequestStack $requestStack
    ) {}

    /**
     * Log an admin action
     */
    public function log(
        string $actionType,
        ?string $entity = null,
        ?int $entityId = null,
        ?string $details = null
    ): AuditLog {
        /** @var Admin|null $admin */
        $admin = $this->security->getUser();
        
        if (!$admin instanceof Admin) {
            throw new \LogicException('Cannot log action: no authenticated admin found.');
        }

        $auditLog = new AuditLog();
        $auditLog->setAdmin($admin);
        $auditLog->setActionType($actionType);
        $auditLog->setEntity($entity);
        $auditLog->setEntityId($entityId);
        $auditLog->setDetails($details);

        // Get request information if available
        $request = $this->requestStack->getCurrentRequest();
        if ($request) {
            $auditLog->setIpAddress($request->getClientIp());
            $auditLog->setUserAgent($request->headers->get('User-Agent'));
        }

        $this->entityManager->persist($auditLog);
        $this->entityManager->flush();

        return $auditLog;
    }

    /**
     * Log login action
     */
    public function logLogin(Admin $admin): AuditLog
    {
        return $this->log(
            'Admin Login',
            'Admin',
            $admin->getAdminId(),
            "Admin {$admin->getUsername()} logged in"
        );
    }

    /**
     * Log logout action
     */
    public function logLogout(Admin $admin): AuditLog
    {
        return $this->log(
            'Admin Logout',
            'Admin',
            $admin->getAdminId(),
            "Admin {$admin->getUsername()} logged out"
        );
    }

    /**
     * Log failed login attempt
     */
    public function logFailedLogin(string $username): AuditLog
    {
        // Create a temporary audit log without admin (since login failed)
        $auditLog = new AuditLog();
        $auditLog->setActionType('Failed Login Attempt');
        $auditLog->setEntity('Admin');
        $auditLog->setDetails("Failed login attempt for username: {$username}");

        // Get request information if available
        $request = $this->requestStack->getCurrentRequest();
        if ($request) {
            $auditLog->setIpAddress($request->getClientIp());
            $auditLog->setUserAgent($request->headers->get('User-Agent'));
        }

        // We need to handle this differently since we don't have an admin
        // For now, we'll skip this log or create a system log
        // In a real application, you might want to log this to a separate table
        
        return $auditLog;
    }

    /**
     * Log client creation
     */
    public function logClientCreation(int $clientId, string $clientName): AuditLog
    {
        return $this->log(
            'Created Client',
            'Client',
            $clientId,
            "Created new client: {$clientName}"
        );
    }

    /**
     * Log account creation
     */
    public function logAccountCreation(int $accountId, string $accountType, int $clientId): AuditLog
    {
        return $this->log(
            'Created Account',
            'Account',
            $accountId,
            "Created new {$accountType} account for client ID: {$clientId}"
        );
    }

    /**
     * Log card issuance
     */
    public function logCardIssuance(int $cardId, int $accountId): AuditLog
    {
        return $this->log(
            'Issued Card',
            'Card',
            $cardId,
            "Issued new card for account ID: {$accountId}"
        );
    }

    /**
     * Log transaction
     */
    public function logTransaction(int $transactionId, string $type, string $amount, int $accountId): AuditLog
    {
        return $this->log(
            'Processed Transaction',
            'Transaction',
            $transactionId,
            "Processed {$type} transaction of {$amount} for account ID: {$accountId}"
        );
    }

    /**
     * Log security action
     */
    public function logSecurityAction(string $action, ?string $details = null): AuditLog
    {
        return $this->log(
            "Security: {$action}",
            'Security',
            null,
            $details
        );
    }

    /**
     * Log system action
     */
    public function logSystemAction(string $action, ?string $details = null): AuditLog
    {
        return $this->log(
            "System: {$action}",
            'System',
            null,
            $details
        );
    }

    /**
     * Get recent logs for dashboard
     */
    public function getRecentLogs(int $limit = 10): array
    {
        return $this->auditLogRepository->getRecentLogs($limit);
    }

    /**
     * Get logs for specific admin
     */
    public function getLogsForAdmin(Admin $admin, int $limit = 50): array
    {
        return $this->auditLogRepository->findByAdmin($admin, $limit);
    }

    /**
     * Search logs
     */
    public function searchLogs(string $query): array
    {
        return $this->auditLogRepository->search($query);
    }

    /**
     * Clean old logs
     */
    public function cleanOldLogs(int $daysToKeep = 365): int
    {
        return $this->auditLogRepository->cleanOldLogs($daysToKeep);
    }
}
