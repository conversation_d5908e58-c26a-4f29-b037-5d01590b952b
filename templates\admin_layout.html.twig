{% extends 'base.html.twig' %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <!-- Logo -->
                <div class="text-center mb-4">
                    <img src="{{ asset('images/VaultBoard.png') }}" alt="VaultBoard" class="img-fluid" style="height: 50px;">
                    <h5 class="mt-2 mb-0">VaultBoard</h5>
                    <small class="text-light opacity-75">Admin Panel</small>
                </div>

                <!-- Navigation -->
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_dashboard' ? 'active' : '' }}" 
                           href="{{ path('admin_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_clients' ? 'active' : '' }}" 
                           href="{{ path('admin_clients_index') }}">
                            <i class="fas fa-users"></i>
                            Clients
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_accounts' ? 'active' : '' }}" 
                           href="#" onclick="alert('Coming soon!')">
                            <i class="fas fa-university"></i>
                            Accounts
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_cards' ? 'active' : '' }}" 
                           href="#" onclick="alert('Coming soon!')">
                            <i class="fas fa-credit-card"></i>
                            Cards
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_transactions' ? 'active' : '' }}" 
                           href="#" onclick="alert('Coming soon!')">
                            <i class="fas fa-exchange-alt"></i>
                            Transactions
                        </a>
                    </li>
                    
                    <hr class="my-3 text-light opacity-25">
                    
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_reports' ? 'active' : '' }}" 
                           href="{{ path('admin_reports') }}">
                            <i class="fas fa-chart-bar"></i>
                            Reports
                        </a>
                    </li>
                    
                    {% if is_granted('ROLE_SUPER_ADMIN') %}
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_admins' ? 'active' : '' }}" 
                           href="#" onclick="alert('Coming soon!')">
                            <i class="fas fa-user-shield"></i>
                            Admins
                        </a>
                    </li>
                    {% endif %}
                    
                    <li class="nav-item">
                        <a class="nav-link {{ app.request.get('_route') starts with 'admin_audit' ? 'active' : '' }}" 
                           href="#" onclick="alert('Coming soon!')">
                            <i class="fas fa-history"></i>
                            Audit Logs
                        </a>
                    </li>
                </ul>
                
                <!-- User Info -->
                <div class="mt-auto pt-4 border-top border-light border-opacity-25">
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" 
                           href="#" 
                           id="userDropdown" 
                           role="button" 
                           data-bs-toggle="dropdown" 
                           aria-expanded="false">
                            <i class="fas fa-user-circle me-2"></i>
                            <div class="flex-grow-1">
                                <div class="fw-semibold">{{ app.user.username }}</div>
                                <small class="text-light opacity-75">{{ app.user.role.label }}</small>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark">
                            <li>
                                <a class="dropdown-item" href="{{ path('admin_profile') }}">
                                    <i class="fas fa-user me-2"></i>Profile
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ path('admin_logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <!-- Top bar -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">{% block page_title %}Dashboard{% endblock %}</h1>
                
                <div class="btn-toolbar mb-2 mb-md-0">
                    {% block page_actions %}{% endblock %}
                    
                    <!-- Notifications -->
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-secondary position-relative" 
                                type="button" 
                                id="notificationsDropdown" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            {% if app.user.unreadNotificationsCount > 0 %}
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    {{ app.user.unreadNotificationsCount }}
                                </span>
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <li><h6 class="dropdown-header">Notifications</h6></li>
                            {% if app.user.unreadNotificationsCount > 0 %}
                                <li><a class="dropdown-item small" href="#">You have {{ app.user.unreadNotificationsCount }} unread notifications</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#" onclick="alert('Coming soon!')">View All</a></li>
                            {% else %}
                                <li><span class="dropdown-item-text small text-muted">No new notifications</span></li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Flash messages -->
            {% for type, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                        {% if type == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elseif type == 'error' or type == 'danger' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% elseif type == 'warning' %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                        {% else %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endfor %}

            <!-- Page content -->
            {% block content %}{% endblock %}
        </main>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    // Auto-dismiss alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    });
</script>
{% endblock %}
