<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}VaultBoard - Bank Admin System{% endblock %}</title>

        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Font Awesome -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <!-- Custom CSS -->
        <link rel="icon" href="{{ asset('images/VaultBoard.png') }}">

        {% block stylesheets %}
        <style>
            :root {
                --vb-primary: #2c3e50;
                --vb-secondary: #3498db;
                --vb-success: #27ae60;
                --vb-danger: #e74c3c;
                --vb-warning: #f39c12;
                --vb-info: #17a2b8;
                --vb-light: #f8f9fa;
                --vb-dark: #343a40;
            }

            .navbar-brand img {
                height: 40px;
                width: auto;
            }

            .sidebar {
                min-height: 100vh;
                background: linear-gradient(135deg, var(--vb-primary), var(--vb-secondary));
                color: white;
            }

            .sidebar .nav-link {
                color: rgba(255, 255, 255, 0.8);
                padding: 0.75rem 1rem;
                border-radius: 0.375rem;
                margin: 0.25rem 0;
                transition: all 0.3s ease;
            }

            .sidebar .nav-link:hover,
            .sidebar .nav-link.active {
                color: white;
                background-color: rgba(255, 255, 255, 0.1);
                transform: translateX(5px);
            }

            .sidebar .nav-link i {
                width: 20px;
                margin-right: 10px;
            }

            .main-content {
                background-color: #f8f9fa;
                min-height: 100vh;
            }

            .card {
                border: none;
                border-radius: 10px;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                transition: all 0.3s ease;
            }

            .card:hover {
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                transform: translateY(-2px);
            }

            .stat-card {
                background: linear-gradient(135deg, var(--vb-primary), var(--vb-secondary));
                color: white;
            }

            .stat-card .card-body {
                padding: 1.5rem;
            }

            .stat-card .stat-icon {
                font-size: 2.5rem;
                opacity: 0.8;
            }

            .badge {
                font-size: 0.75em;
                padding: 0.375rem 0.75rem;
            }

            .table th {
                border-top: none;
                font-weight: 600;
                color: var(--vb-primary);
            }

            .btn-primary {
                background-color: var(--vb-primary);
                border-color: var(--vb-primary);
            }

            .btn-primary:hover {
                background-color: var(--vb-secondary);
                border-color: var(--vb-secondary);
            }

            .alert {
                border: none;
                border-radius: 10px;
            }

            .login-container {
                background: linear-gradient(135deg, var(--vb-primary), var(--vb-secondary));
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .login-card {
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                overflow: hidden;
                max-width: 400px;
                width: 100%;
            }

            .login-header {
                background: linear-gradient(135deg, var(--vb-primary), var(--vb-secondary));
                color: white;
                padding: 2rem;
                text-align: center;
            }

            .login-header img {
                height: 60px;
                margin-bottom: 1rem;
            }
        </style>
        {% endblock %}
    </head>
    <body>
        {% block body %}{% endblock %}

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

        {% block javascripts %}
            {% block importmap %}{{ importmap('app') }}{% endblock %}
        {% endblock %}
    </body>
</html>
