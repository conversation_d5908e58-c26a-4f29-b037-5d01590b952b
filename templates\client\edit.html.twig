{% extends 'admin_layout.html.twig' %}

{% block title %}Edit {{ client.fullName }} - VaultBoard{% endblock %}

{% block page_title %}Edit Client{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{{ path('admin_clients_show', {id: client.id}) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Back to Client
    </a>
    <a href="{{ path('admin_clients_index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-list me-1"></i>All Clients
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-edit me-2"></i>Edit Client Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">
                                    First Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="first_name" 
                                       name="first_name" 
                                       value="{{ client.firstName }}" 
                                       required 
                                       autofocus>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">
                                    Last Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="last_name" 
                                       name="last_name" 
                                       value="{{ client.lastName }}" 
                                       required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            Email Address <span class="text-danger">*</span>
                        </label>
                        <input type="email" 
                               class="form-control" 
                               id="email" 
                               name="email" 
                               value="{{ client.email }}" 
                               required>
                        <div class="form-text">This will be used for all communications with the client.</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" 
                               class="form-control" 
                               id="phone" 
                               name="phone" 
                               value="{{ client.phone }}" 
                               placeholder="+****************">
                        <div class="form-text">Optional. Include country code if international.</div>
                    </div>

                    <div class="mb-4">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            {% for status in statuses %}
                            <option value="{{ status.value }}" 
                                    {{ client.status == status ? 'selected' : '' }}>
                                {{ status.label }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            {% if client.activeAccount %}
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                This client has an active account. Changing status may affect account operations.
                            {% endif %}
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ path('admin_clients_show', {id: client.id}) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update Client
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Client Summary -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Client Summary
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Client ID:</dt>
                            <dd class="col-sm-7"><code>{{ client.clientId }}</code></dd>
                            
                            <dt class="col-sm-5">Created:</dt>
                            <dd class="col-sm-7">{{ client.createdAt|date('M d, Y') }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Total Accounts:</dt>
                            <dd class="col-sm-7">{{ client.accounts|length }}</dd>
                            
                            <dt class="col-sm-5">Active Accounts:</dt>
                            <dd class="col-sm-7">
                                {% if client.activeAccount %}
                                    <span class="badge badge-success">1</span>
                                {% else %}
                                    <span class="badge badge-secondary">0</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Format phone number as user types
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        e.target.value = value;
    });

    // Validate email format
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function(e) {
        const email = e.target.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            e.target.classList.add('is-invalid');
        } else {
            e.target.classList.remove('is-invalid');
        }
    });

    // Warn about status changes if client has active account
    const statusSelect = document.getElementById('status');
    const hasActiveAccount = {{ client.activeAccount ? 'true' : 'false' }};
    
    if (hasActiveAccount) {
        statusSelect.addEventListener('change', function(e) {
            if (e.target.value === 'inactive' || e.target.value === 'blocked') {
                if (!confirm('This client has an active account. Changing the status may affect account operations. Are you sure you want to continue?')) {
                    e.target.value = '{{ client.status.value }}'; // Reset to original value
                }
            }
        });
    }
});
</script>
{% endblock %}
