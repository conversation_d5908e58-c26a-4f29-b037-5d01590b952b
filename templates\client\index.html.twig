{% extends 'admin_layout.html.twig' %}

{% block title %}Clients - VaultBoard{% endblock %}

{% block page_title %}Clients{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{{ path('admin_clients_new') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>New Client
    </a>
    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
        <i class="fas fa-filter me-1"></i>Filter
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="{{ path('admin_clients_index') }}">All Clients</a></li>
        {% for status in statuses %}
        <li>
            <a class="dropdown-item {{ current_status == status.value ? 'active' : '' }}" 
               href="{{ path('admin_clients_index', {status: status.value}) }}">
                <span class="badge {{ status.badgeClass }} me-2">{{ status.label }}</span>
                {{ status.label }} Clients
            </a>
        </li>
        {% endfor %}
    </ul>
</div>
{% endblock %}

{% block content %}
<!-- Search and Filter Bar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="{{ search }}" 
                           placeholder="Search by name or email...">
                </div>
            </div>
            <div class="col-md-4">
                <select name="status" class="form-select">
                    <option value="">All Statuses</option>
                    {% for status in statuses %}
                    <option value="{{ status.value }}" {{ current_status == status.value ? 'selected' : '' }}>
                        {{ status.label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Clients Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>
            Clients List
            {% if search %}
                <small class="text-muted">(Search: "{{ search }}")</small>
            {% elseif current_status %}
                <small class="text-muted">(Status: {{ current_status|title }})</small>
            {% endif %}
        </h5>
        <span class="badge bg-secondary">{{ clients|length }} clients</span>
    </div>
    <div class="card-body p-0">
        {% if clients|length > 0 %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Client ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Accounts</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                        <tr>
                            <td>
                                <code>{{ client.clientId }}</code>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ client.firstName|first|upper }}{{ client.lastName|first|upper }}
                                    </div>
                                    <div>
                                        <a href="{{ path('admin_clients_show', {id: client.id}) }}" 
                                           class="text-decoration-none fw-semibold">
                                            {{ client.fullName }}
                                        </a>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="mailto:{{ client.email }}" class="text-decoration-none">
                                    {{ client.email }}
                                </a>
                            </td>
                            <td>
                                {% if client.phone %}
                                    <a href="tel:{{ client.phone }}" class="text-decoration-none">
                                        {{ client.phone }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">—</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {{ client.status.badgeClass }}">
                                    {{ client.status.label }}
                                </span>
                            </td>
                            <td>
                                {% set activeAccount = client.activeAccount %}
                                {% if activeAccount %}
                                    <span class="badge badge-success">
                                        <i class="fas fa-university me-1"></i>
                                        {{ activeAccount.accountType.label }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">No active account</span>
                                {% endif %}
                            </td>
                            <td>
                                <span title="{{ client.createdAt|date('Y-m-d H:i:s') }}">
                                    {{ client.createdAt|date('M d, Y') }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ path('admin_clients_show', {id: client.id}) }}" 
                                       class="btn btn-outline-primary" 
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if is_granted('client_update', app.user) %}
                                    <a href="{{ path('admin_clients_edit', {id: client.id}) }}" 
                                       class="btn btn-outline-secondary" 
                                       title="Edit Client">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if is_granted('client_update', app.user) %}
                                    <form method="POST" 
                                          action="{{ path('admin_clients_toggle_status', {id: client.id}) }}" 
                                          class="d-inline">
                                        <input type="hidden" name="_token" value="{{ csrf_token('toggle_status' ~ client.id) }}">
                                        <button type="submit" 
                                                class="btn btn-outline-warning" 
                                                title="Toggle Status"
                                                onclick="return confirm('Are you sure you want to change the client status?')">
                                            <i class="fas fa-toggle-on"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No clients found</h5>
                {% if search or current_status %}
                    <p class="text-muted">Try adjusting your search criteria or filters.</p>
                    <a href="{{ path('admin_clients_index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                {% else %}
                    <p class="text-muted">Get started by creating your first client.</p>
                    <a href="{{ path('admin_clients_new') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create First Client
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}
</style>
{% endblock %}
