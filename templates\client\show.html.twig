{% extends 'admin_layout.html.twig' %}

{% block title %}{{ client.fullName }} - Clients - VaultBoard{% endblock %}

{% block page_title %}Client Details{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{{ path('admin_clients_index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Back to Clients
    </a>
    {% if is_granted('client_update', app.user) %}
    <a href="{{ path('admin_clients_edit', {id: client.id}) }}" class="btn btn-primary">
        <i class="fas fa-edit me-1"></i>Edit Client
    </a>
    {% endif %}
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Client Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Client Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Client ID:</dt>
                            <dd class="col-sm-8"><code>{{ client.clientId }}</code></dd>
                            
                            <dt class="col-sm-4">Full Name:</dt>
                            <dd class="col-sm-8">{{ client.fullName }}</dd>
                            
                            <dt class="col-sm-4">Email:</dt>
                            <dd class="col-sm-8">
                                <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                            </dd>
                            
                            <dt class="col-sm-4">Phone:</dt>
                            <dd class="col-sm-8">
                                {% if client.phone %}
                                    <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                <span class="badge {{ client.status.badgeClass }}">
                                    {{ client.status.label }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ client.createdAt|date('F j, Y \\a\\t g:i A') }}</dd>
                            
                            <dt class="col-sm-4">Accounts:</dt>
                            <dd class="col-sm-8">{{ client.accounts|length }} total</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Accounts -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-university me-2"></i>Accounts
                </h5>
                {% if client.canHaveNewAccount and is_granted('account_create', app.user) %}
                <button class="btn btn-sm btn-primary" onclick="alert('Account creation coming soon!')">
                    <i class="fas fa-plus me-1"></i>New Account
                </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if client.accounts|length > 0 %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Account Number</th>
                                    <th>Type</th>
                                    <th>Balance</th>
                                    <th>Status</th>
                                    <th>Opened</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in client.accounts %}
                                <tr>
                                    <td><code>{{ account.accountNumber }}</code></td>
                                    <td>
                                        <i class="{{ account.accountType.icon }} me-1"></i>
                                        {{ account.accountType.label }}
                                    </td>
                                    <td>{{ account.formattedBalance }}</td>
                                    <td>
                                        <span class="badge {{ account.status.badgeClass }}">
                                            {{ account.status.label }}
                                        </span>
                                    </td>
                                    <td>{{ account.openedAt|date('M d, Y') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="alert('Account details coming soon!')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-university fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">No accounts found</h6>
                        <p class="text-muted">This client doesn't have any accounts yet.</p>
                        {% if is_granted('account_create', app.user) %}
                        <button class="btn btn-primary" onclick="alert('Account creation coming soon!')">
                            <i class="fas fa-plus me-1"></i>Create First Account
                        </button>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if is_granted('client_update', app.user) %}
                    <a href="{{ path('admin_clients_edit', {id: client.id}) }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Edit Client
                    </a>
                    {% endif %}
                    
                    {% if client.canHaveNewAccount and is_granted('account_create', app.user) %}
                    <button class="btn btn-outline-success" onclick="alert('Account creation coming soon!')">
                        <i class="fas fa-plus me-2"></i>Create Account
                    </button>
                    {% endif %}
                    
                    {% if is_granted('client_update', app.user) %}
                    <form method="POST" action="{{ path('admin_clients_toggle_status', {id: client.id}) }}">
                        <input type="hidden" name="_token" value="{{ csrf_token('toggle_status' ~ client.id) }}">
                        <button type="submit" 
                                class="btn btn-outline-warning w-100"
                                onclick="return confirm('Are you sure you want to change the client status?')">
                            <i class="fas fa-toggle-on me-2"></i>
                            {% if client.status.value == 'active' %}Deactivate{% else %}Activate{% endif %} Client
                        </button>
                    </form>
                    {% endif %}
                    
                    {% if is_granted('client_delete', app.user) and not client.activeAccount %}
                    <form method="POST" action="{{ path('admin_clients_delete', {id: client.id}) }}">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ client.id) }}">
                        <button type="submit" 
                                class="btn btn-outline-danger w-100"
                                onclick="return confirm('Are you sure you want to delete this client? This action cannot be undone.')">
                            <i class="fas fa-trash me-2"></i>Delete Client
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Client Summary -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="mb-1">{{ client.accounts|length }}</h4>
                            <small class="text-muted">Total Accounts</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="mb-1">
                            {% if client.activeAccount %}1{% else %}0{% endif %}
                        </h4>
                        <small class="text-muted">Active Accounts</small>
                    </div>
                </div>
                
                {% if client.activeAccount %}
                <hr>
                <div class="text-center">
                    <h5 class="mb-1">{{ client.activeAccount.formattedBalance }}</h5>
                    <small class="text-muted">Total Balance</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
