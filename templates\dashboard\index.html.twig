{% extends 'admin_layout.html.twig' %}

{% block title %}Dashboard - VaultBoard{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{{ path('admin_clients_new') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>New Client
    </a>
    <a href="{{ path('admin_reports') }}" class="btn btn-outline-secondary">
        <i class="fas fa-chart-bar me-1"></i>Reports
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Clients</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_clients|number_format }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Accounts</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_accounts|number_format }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-university stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Balance</div>
                        <div class="h5 mb-0 font-weight-bold">${{ stats.total_balance|number_format(2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Transactions</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_transactions|number_format }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Row -->
{% if stats.pending_transactions > 0 or stats.failed_transactions > 0 or cards_expiring_soon|length > 0 or low_balance_accounts|length > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Alerts & Notifications
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if stats.pending_transactions > 0 %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-clock me-2"></i>
                            <strong>{{ stats.pending_transactions }}</strong> pending transactions
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if stats.failed_transactions > 0 %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="alert alert-danger mb-0">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>{{ stats.failed_transactions }}</strong> failed transactions
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if cards_expiring_soon|length > 0 %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            <strong>{{ cards_expiring_soon|length }}</strong> cards expiring soon
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if low_balance_accounts|length > 0 %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>{{ low_balance_accounts|length }}</strong> low balance accounts
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Content Row -->
<div class="row">
    <!-- Recent Clients -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Recent Clients
                </h5>
                <a href="{{ path('admin_clients_index') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_clients|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for client in recent_clients %}
                                <tr>
                                    <td>
                                        <a href="{{ path('admin_clients_show', {id: client.id}) }}" class="text-decoration-none">
                                            {{ client.fullName }}
                                        </a>
                                    </td>
                                    <td>{{ client.email }}</td>
                                    <td>
                                        <span class="badge {{ client.status.badgeClass }}">
                                            {{ client.status.label }}
                                        </span>
                                    </td>
                                    <td>{{ client.createdAt|date('M d, Y') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted mb-0">No recent clients found.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>Recent Transactions
                </h5>
                <a href="#" onclick="alert('Coming soon!')" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_transactions|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>
                                        <i class="{{ transaction.type.icon }} me-1"></i>
                                        {{ transaction.type.label }}
                                    </td>
                                    <td>{{ transaction.formattedAmount }}</td>
                                    <td>
                                        <span class="badge {{ transaction.status.badgeClass }}">
                                            {{ transaction.status.label }}
                                        </span>
                                    </td>
                                    <td>{{ transaction.timestamp|date('M d, H:i') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted mb-0">No recent transactions found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Activity
                </h5>
                <a href="#" onclick="alert('Coming soon!')" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_audit_logs|length > 0 %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Admin</th>
                                    <th>Details</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_audit_logs %}
                                <tr>
                                    <td>
                                        <i class="{{ log.actionIcon }} text-{{ log.severityLevel }} me-2"></i>
                                        {{ log.actionType }}
                                    </td>
                                    <td>{{ log.admin.username }}</td>
                                    <td>{{ log.details|default('—') }}</td>
                                    <td>{{ log.timestamp|date('M d, Y H:i') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted mb-0">No recent activity found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
