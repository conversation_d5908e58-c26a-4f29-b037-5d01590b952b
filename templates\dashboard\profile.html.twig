{% extends 'admin_layout.html.twig' %}

{% block title %}Profile - VaultBoard{% endblock %}

{% block page_title %}My Profile{% endblock %}

{% block content %}
<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Profile Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Admin ID:</dt>
                            <dd class="col-sm-8"><code>{{ admin.adminId }}</code></dd>
                            
                            <dt class="col-sm-4">Username:</dt>
                            <dd class="col-sm-8">{{ admin.username }}</dd>
                            
                            <dt class="col-sm-4">Role:</dt>
                            <dd class="col-sm-8">
                                <span class="badge {{ admin.role.badgeClass }}">
                                    {{ admin.role.label }}
                                </span>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                <span class="badge {{ admin.status.badgeClass }}">
                                    {{ admin.status.label }}
                                </span>
                            </dd>
                            
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ admin.createdAt|date('F j, Y \\a\\t g:i A') }}</dd>
                            
                            <dt class="col-sm-4">Permissions:</dt>
                            <dd class="col-sm-8">{{ admin.role.permissions|length }} permissions</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>My Recent Activity
                </h5>
                <span class="badge bg-secondary">{{ recent_activity|length }} actions</span>
            </div>
            <div class="card-body">
                {% if recent_activity|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Entity</th>
                                    <th>Details</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_activity %}
                                <tr>
                                    <td>
                                        <i class="{{ log.actionIcon }} text-{{ log.severityLevel }} me-2"></i>
                                        {{ log.actionType }}
                                    </td>
                                    <td>
                                        {% if log.entity %}
                                            <span class="badge badge-light">{{ log.entity }}</span>
                                            {% if log.entityId %}
                                                <small class="text-muted">#{{ log.entityId }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">—</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.details|default('—') }}</td>
                                    <td>
                                        <span title="{{ log.timestamp|date('Y-m-d H:i:s') }}">
                                            {{ log.timestamp|date('M d, H:i') }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">No recent activity</h6>
                        <p class="text-muted">Your actions will appear here once you start using the system.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>My Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="mb-1">{{ recent_activity|length }}</h4>
                            <small class="text-muted">Recent Actions</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="mb-1">{{ notifications|length }}</h4>
                        <small class="text-muted">Notifications</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>My Permissions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for permission in admin.role.permissions %}
                        <div class="col-12 mb-2">
                            <span class="badge badge-light w-100 text-start">
                                <i class="fas fa-check text-success me-2"></i>
                                {{ permission|replace({'_': ' '})|title }}
                            </span>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Notifications -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>My Notifications
                </h5>
                {% if notifications|length > 0 %}
                <span class="badge bg-primary">{{ notifications|length }}</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if notifications|length > 0 %}
                    {% for notification in notifications|slice(0, 5) %}
                    <div class="d-flex align-items-start mb-3 {% if not notification.isRead %}bg-light p-2 rounded{% endif %}">
                        <i class="{{ notification.icon }} me-3 mt-1"></i>
                        <div class="flex-grow-1">
                            <div class="small">{{ notification.message }}</div>
                            <small class="text-muted">{{ notification.timeAgo }}</small>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if notifications|length > 5 %}
                    <div class="text-center">
                        <button class="btn btn-sm btn-outline-primary" onclick="alert('Full notifications view coming soon!')">
                            View All ({{ notifications|length }})
                        </button>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No notifications</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
