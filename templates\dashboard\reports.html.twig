{% extends 'admin_layout.html.twig' %}

{% block title %}Reports - VaultBoard{% endblock %}

{% block page_title %}Reports & Analytics{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
        <i class="fas fa-print me-1"></i>Print Report
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="alert('Export feature coming soon!')">
        <i class="fas fa-download me-1"></i>Export
    </button>
</div>
{% endblock %}

{% block content %}
<!-- Date Range Info -->
<div class="alert alert-info mb-4">
    <i class="fas fa-calendar me-2"></i>
    <strong>Report Period:</strong> {{ date_range.start|date('F j, Y') }} to {{ date_range.end|date('F j, Y') }}
    (Last 30 days)
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                <h4>{{ account_stats.open + account_stats.closed + account_stats.frozen }}</h4>
                <p class="text-muted mb-0">Total Accounts</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-credit-card fa-2x text-success mb-3"></i>
                <h4>{{ card_stats.active + card_stats.expired + card_stats.lost + card_stats.blocked }}</h4>
                <p class="text-muted mb-0">Total Cards</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exchange-alt fa-2x text-info mb-3"></i>
                <h4>{{ transaction_stats.success + transaction_stats.failed + transaction_stats.pending }}</h4>
                <p class="text-muted mb-0">Total Transactions</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-percentage fa-2x text-warning mb-3"></i>
                <h4>
                    {% set total_transactions = transaction_stats.success + transaction_stats.failed + transaction_stats.pending %}
                    {% if total_transactions > 0 %}
                        {{ ((transaction_stats.success / total_transactions) * 100)|number_format(1) }}%
                    {% else %}
                        0%
                    {% endif %}
                </h4>
                <p class="text-muted mb-0">Success Rate</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Account Status Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Account Status Distribution
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Count</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total_accounts = account_stats.open + account_stats.closed + account_stats.frozen %}
                            {% for status, count in account_stats %}
                            <tr>
                                <td>
                                    <span class="badge badge-{{ status == 'open' ? 'success' : (status == 'closed' ? 'secondary' : 'warning') }}">
                                        {{ status|title }}
                                    </span>
                                </td>
                                <td>{{ count }}</td>
                                <td>
                                    {% if total_accounts > 0 %}
                                        {{ ((count / total_accounts) * 100)|number_format(1) }}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Type Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Transaction Types
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Count</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total_transactions = transaction_type_stats.deposit + transaction_type_stats.withdrawal + transaction_type_stats.transfer_out + transaction_type_stats.transfer_in %}
                            {% for type, count in transaction_type_stats %}
                            <tr>
                                <td>
                                    <span class="badge badge-{{ type == 'deposit' ? 'success' : (type == 'withdrawal' ? 'danger' : 'info') }}">
                                        {{ type|replace({'_': ' '})|title }}
                                    </span>
                                </td>
                                <td>{{ count }}</td>
                                <td>
                                    {% if total_transactions > 0 %}
                                        {{ ((count / total_transactions) * 100)|number_format(1) }}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Transaction Status Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-success">{{ transaction_stats.success|number_format }}</h3>
                            <p class="text-muted mb-0">Successful</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-warning">{{ transaction_stats.pending|number_format }}</h3>
                            <p class="text-muted mb-0">Pending</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-danger">{{ transaction_stats.failed|number_format }}</h3>
                            <p class="text-muted mb-0">Failed</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Daily Activity (if data available) -->
{% if daily_volume|length > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>Daily Transaction Volume (Last 30 Days)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Transaction Count</th>
                                <th>Total Volume</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for day in daily_volume|slice(-10) %}
                            <tr>
                                <td>{{ day.date|date('M d, Y') }}</td>
                                <td>{{ day.count|number_format }}</td>
                                <td>${{ day.volume|number_format(2) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if daily_volume|length > 10 %}
                <p class="text-muted small mb-0">Showing last 10 days. Full data available in detailed reports.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- System Health -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>System Health Indicators
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <div>
                                <div class="fw-semibold">Database</div>
                                <small class="text-muted">Connected</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <div>
                                <div class="fw-semibold">Authentication</div>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <div>
                                <div class="fw-semibold">Audit Logging</div>
                                <small class="text-muted">Enabled</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <div>
                                <div class="fw-semibold">Security</div>
                                <small class="text-muted">Protected</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
@media print {
    .sidebar, .btn-toolbar, .alert {
        display: none !important;
    }
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
}
</style>
{% endblock %}
