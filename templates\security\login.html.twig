{% extends 'base.html.twig' %}

{% block title %}Login - VaultBoard{% endblock %}

{% block body %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <img src="{{ asset('images/VaultBoard.png') }}" alt="VaultBoard" class="img-fluid">
            <h3 class="mb-0">VaultBoard</h3>
            <p class="mb-0">Bank Admin System</p>
        </div>
        
        <div class="p-4">
            {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ error.messageKey|trans(error.messageData, 'security') }}
                </div>
            {% endif %}

            <form method="post">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>Username
                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="username" 
                           name="username" 
                           value="{{ last_username }}" 
                           required 
                           autofocus
                           placeholder="Enter your username">
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                    <input type="password" 
                           class="form-control" 
                           id="password" 
                           name="password" 
                           required
                           placeholder="Enter your password">
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember_me" name="_remember_me">
                    <label class="form-check-label" for="remember_me">
                        Remember me
                    </label>
                </div>

                <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </button>
                </div>
            </form>
        </div>
        
        <div class="text-center p-3 border-top">
            <small class="text-muted">
                <i class="fas fa-shield-alt me-1"></i>
                Secure Admin Access Only
            </small>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    // Auto-focus on username field if empty, otherwise focus on password
    document.addEventListener('DOMContentLoaded', function() {
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        if (usernameField.value.trim() === '') {
            usernameField.focus();
        } else {
            passwordField.focus();
        }
    });
</script>
{% endblock %}
